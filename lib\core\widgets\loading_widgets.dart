import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/core/theme/design_tokens.dart';

/// Standardized loading widgets for consistent UX across the app
/// Provides various loading indicators that integrate with GetX state management
class LoadingWidgets {
  LoadingWidgets._();

  /// Primary loading indicator with app branding
  static Widget primaryLoader({
    double? size,
    Color? color,
    String? message,
  }) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: size ?? 40.w,
            height: size ?? 40.w,
            child: CircularProgressIndicator(
              strokeWidth: 3.w,
              valueColor: AlwaysStoppedAnimation<Color>(
                color ?? AppColor.primaryColor,
              ),
            ),
          ),
          if (message != null) ...[
            SizedBox(height: DesignTokens.spaceMD),
            Text(
              message,
              style: TextStyle(
                fontSize: 14.sp,
                color: AppColor.textColor,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }

  /// Small loading indicator for buttons and inline elements
  static Widget smallLoader({
    Color? color,
    double? size,
  }) {
    return SizedBox(
      width: size ?? 20.w,
      height: size ?? 20.w,
      child: CircularProgressIndicator(
        strokeWidth: 2.w,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColor.whiteColor,
        ),
      ),
    );
  }

  /// Loading overlay that covers the entire screen
  static Widget loadingOverlay({
    String? message,
    bool dismissible = false,
  }) {
    return Container(
      color: AppColor.blackColor.withValues(alpha: 0.5),
      child: Center(
        child: Container(
          padding: EdgeInsets.all(DesignTokens.spaceLG),
          margin: EdgeInsets.symmetric(horizontal: DesignTokens.spaceXL),
          decoration: BoxDecoration(
            color: AppColor.whiteColor,
            borderRadius: BorderRadius.circular(DesignTokens.radiusLG),
            boxShadow: [
              BoxShadow(
                color: AppColor.blackColor.withValues(alpha: 0.1),
                blurRadius: 10.r,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              primaryLoader(),
              if (message != null) ...[
                SizedBox(height: DesignTokens.spaceMD),
                Text(
                  message,
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: AppColor.textColor,
                    fontWeight: FontWeight.w500,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
              if (dismissible) ...[
                SizedBox(height: DesignTokens.spaceMD),
                TextButton(
                  onPressed: () => Get.back(),
                  child: Text(
                    'إلغاء',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColor.primaryColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Shimmer loading effect for list items and cards
  static Widget shimmerLoader({
    double? width,
    double? height,
    BorderRadius? borderRadius,
  }) {
    return Container(
      width: width ?? double.infinity,
      height: height ?? 60.h,
      decoration: BoxDecoration(
        color: AppColor.borderColor.withValues(alpha: 0.3),
        borderRadius: borderRadius ?? BorderRadius.circular(DesignTokens.radiusMD),
      ),
      child: const _ShimmerEffect(),
    );
  }

  /// Loading state for lists with multiple shimmer items
  static Widget listShimmerLoader({
    int itemCount = 5,
    double? itemHeight,
    EdgeInsets? padding,
  }) {
    return ListView.separated(
      padding: padding ?? EdgeInsets.all(DesignTokens.spaceMD),
      itemCount: itemCount,
      separatorBuilder: (context, index) => SizedBox(height: DesignTokens.spaceSM),
      itemBuilder: (context, index) => shimmerLoader(
        height: itemHeight ?? 80.h,
      ),
    );
  }

  /// Loading button state
  static Widget loadingButton({
    required String text,
    required VoidCallback? onPressed,
    required bool isLoading,
    double? width,
    double? height,
    Color? backgroundColor,
    Color? textColor,
  }) {
    return SizedBox(
      width: width ?? double.infinity,
      height: height ?? DesignTokens.buttonHeightLG,
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor ?? AppColor.primaryColor,
          foregroundColor: textColor ?? AppColor.whiteColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(DesignTokens.radiusMD),
          ),
        ),
        child: isLoading
            ? smallLoader(color: textColor ?? AppColor.whiteColor)
            : Text(
                text,
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                ),
              ),
      ),
    );
  }

  /// Show loading dialog
  static void showLoadingDialog({
    String? message,
    bool dismissible = false,
  }) {
    Get.dialog(
      loadingOverlay(
        message: message,
        dismissible: dismissible,
      ),
      barrierDismissible: dismissible,
    );
  }

  /// Hide loading dialog
  static void hideLoadingDialog() {
    if (Get.isDialogOpen == true) {
      Get.back();
    }
  }

  /// Show loading snackbar
  static void showLoadingSnackbar({
    required String message,
    Duration? duration,
  }) {
    Get.showSnackbar(
      GetSnackBar(
        message: message,
        duration: duration ?? const Duration(seconds: 2),
        showProgressIndicator: true,
        progressIndicatorBackgroundColor: AppColor.whiteColor.withValues(alpha: 0.3),
        progressIndicatorValueColor: AlwaysStoppedAnimation<Color>(AppColor.whiteColor),
        backgroundColor: AppColor.primaryColor,
        borderRadius: DesignTokens.radiusMD,
        margin: EdgeInsets.all(DesignTokens.spaceMD),
        snackPosition: SnackPosition.TOP,
      ),
    );
  }
}

/// Internal shimmer effect widget
class _ShimmerEffect extends StatefulWidget {
  const _ShimmerEffect();

  @override
  State<_ShimmerEffect> createState() => _ShimmerEffectState();
}

class _ShimmerEffectState extends State<_ShimmerEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(
      begin: -1.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    _animationController.repeat();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              stops: [
                _animation.value - 0.3,
                _animation.value,
                _animation.value + 0.3,
              ],
              colors: [
                AppColor.borderColor.withValues(alpha: 0.1),
                AppColor.whiteColor.withValues(alpha: 0.8),
                AppColor.borderColor.withValues(alpha: 0.1),
              ],
            ),
          ),
        );
      },
    );
  }
}
