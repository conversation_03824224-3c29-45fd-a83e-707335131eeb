import 'package:flutter/material.dart';

// Widget for displaying star ratings with customizable size
class RatingStarsWidget extends StatelessWidget {
  const RatingStarsWidget({
    super.key,
    required this.rating,
    this.sizeIcon = 15,
  });
  
  final double? sizeIcon; // Size of star icons
  final double rating; // Rating value to display
  // Builds a row of 5 stars with appropriate coloring based on rating
  @override
  Widget build(BuildContext context) {
    // Calculate number of stars to color based on rating (with half-star rounding)
    int coloredStars = (rating >= 0 && rating % 1 >= 0.5)
        ? rating.toInt() + 1
        : rating.toInt();
    return Row(
      children: List.generate(
        5,
        (index) => Icon(
          index < coloredStars ? Icons.star : Icons.star, // Unfilled star
          color: index < coloredStars ? Colors.amber : null,
          size: sizeIcon,
        ),
      ),
    );
  }
}
