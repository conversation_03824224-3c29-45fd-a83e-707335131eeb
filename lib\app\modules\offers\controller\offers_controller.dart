import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ma3ak/app/modules/my_place/models/create_offer_model.dart';
import 'package:ma3ak/app/modules/offers/models/like_model.dart';
import 'package:ma3ak/app/modules/offers/models/offer_model.dart';
import 'package:ma3ak/app/modules/offers/models/one_place_model.dart';
import 'package:ma3ak/app/modules/offers/models/place_informaion_model.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/models/place_details_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

// Controller for managing offers, creating offers, and handling offer interactions
class OffersController extends GetxController {
  // Loading states and data models
  final isLoading = false.obs;
  final offerData = OfferModel().obs;
  final offerDataByPlace = OfferModel().obs;
  final searchOfferData = OfferModel().obs;
  Rx<CreateOfferModel> createOfferModel = CreateOfferModel().obs;
  RxList<OfferDatum>? searchResult = <OfferDatum>[].obs;
  RxList<OfferDatum>? searchMyOffersResult = <OfferDatum>[].obs;
  Rx<LikeModel>? likeData = LikeModel().obs;
  
  // Form controllers for offer creation
  // final ImagePickerRepo _imagePickerRepo = ImagePickerRepo();
  TextEditingController titleController = TextEditingController();
  TextEditingController desController = TextEditingController();
  TextEditingController typeController = TextEditingController();
  TextEditingController searchController = TextEditingController();
  
  // Image handling variables
  File? selectedImage; // To store the selected image as a File
  Rx<PlaceDetailsModel>? placeDetailsModel = PlaceDetailsModel().obs;
  Rx<PlaceInfromationModel> placeInfromation = PlaceInfromationModel().obs;
  Rx<PlaceInfromationOldModel> placeInfromationOld =
      PlaceInfromationOldModel().obs;

  final List<MultipartFile> imageFiles =
      []; // To store MultipartFile for upload
  final onePlaceData = OnePlaceModel().obs;
  // Method to choose a single image  final ImagePicker _imagePicker = ImagePicker();
  var selectedImagePath = ''.obs;
  final ImagePicker _imagePicker = ImagePicker();

  // Form validation key
  final formKey = GlobalKey<FormState>();

  // Available offer types for selection
  var types = ['وظائف', 'دورات تدريبية', 'مسابقات', 'خصومات', 'اخرى'].obs;

  // The selected type
  var selectedType = ''.obs;

  void searchOffers(String text) {
    if (text.isEmpty) {
      searchResult?.value = offerData.value.data!;
    } else {
      searchResult?.value = offerData.value.data!
          .where(
            (element) =>
                element.title.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
    }
  }

  Future<void> pickOneImage() async {
    try {
      final pickedImage =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        selectedImagePath.value = pickedImage.path;
      } else {
        Get.snackbar('Error', 'No Image Selected');
      }
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }

  Future<MultipartFile?> convertFileToMultipartFile() async {
    if (selectedImagePath.isNotEmpty) {
      return MultipartFile(
        selectedImagePath.value,
        filename: selectedImagePath.value.split('/').last,
        contentType: "multipart/form-data",
      );
    }
    return null;
  }

  Future<Either<String, CreateOfferModel>> createOffer(
      {required String slugPlace}) async {
    isLoading(true);
    final multipartFile = await convertFileToMultipartFile();
    final formData = FormData({
      'title': titleController.text,
      'des': desController.text,
      'type': selectedType.value,
    });
    if (multipartFile == null) {
      Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(MapEntry('img', multipartFile));
    }

    final data = await RemoteServicesImpl().createOffer(
      formData,
      slugPlace: slugPlace,
    );
    isLoading(false);
    data.fold((error) {
      Get.snackbar('Error', error.toString());
    }, (dataModel) async {
      Get.snackbar('Success', 'تم الاضافة بنجاح');
      createOfferModel.value = dataModel;
    });

    return data;
  }

  Future<Either<String, LikeModel>> likeOffer(
      {required String slugOffer}) async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .likeOffer(slugOffer: slugOffer); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      likeData?.value = dataModel;
      debugPrint(dataModel.toString());
    });
    return data;
  }

  Future<Either<String, LikeModel>> disLikeOffer(
      {required String slugOffer}) async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .disLikeOffer(slugOffer: slugOffer); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      likeData?.value = dataModel;
      debugPrint(dataModel.toString());
    });
    return data;
  }

  Future<void> fetchOffersByPlace({required String slugPlace}) async {
    isLoading(true);
    final data = await RemoteServicesImpl().fetchOffersByPlace(
        slugPlace: slugPlace); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      offerDataByPlace.value = dataModel;
      offerDataByPlace.refresh();
      update();
      debugPrint(dataModel.toString());
    });
  }

  Future<void> fetchOffersByType({required String type}) async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchOffersByType(type: type); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      offerDataByPlace.value = dataModel;
      searchResult?.value = dataModel.data!;
      debugPrint(dataModel.toString());
    });
  }

  Future<Either<String, PlaceInfromationOldModel>> getPlaceByOffer(
      {required String slugOffer}) async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .getPlaceByOffer(slugOffer: slugOffer); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      placeInfromationOld.value = dataModel;
      // placeDetailsModel.refresh();
      debugPrint(placeDetailsModel?.value.data.toString());
    });
    return data;
  }

  Future<void> fetchOffers() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchOffers(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      offerData.value = dataModel;
      searchResult?.value = dataModel.data!;
      debugPrint(dataModel.toString());
    });
  }

  @override
  void onInit() async {
    await fetchOffers();
    // searchOfferData.value = offerData.value;
    super.onInit();
  }
}
