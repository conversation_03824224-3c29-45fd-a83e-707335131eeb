import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/main.dart';

/// Authentication middleware for protecting routes that require user login
/// Redirects unauthenticated users to login screen and handles route protection
class AuthMiddleware extends GetMiddleware {
  @override
  int? get priority => 1;

  /// Checks authentication status and redirects if necessary
  /// Returns the appropriate route based on user authentication state
  @override
  RouteSettings? redirect(String? route) {
    // Check if user has valid authentication token
    final token = box.read('token');
    final isAuthenticated = token != null && token.toString().isNotEmpty;

    // List of routes that require authentication
    final protectedRoutes = [
      Routes.navBarView,
      Routes.updatePlace,
      // Add other protected routes here as needed
    ];

    // If trying to access a protected route without authentication
    if (protectedRoutes.contains(route) && !isAuthenticated) {
      // Redirect to login screen
      return const RouteSettings(name: Routes.login);
    }

    // If authenticated user tries to access login/register, redirect to main app
    if (isAuthenticated && (route == Routes.login || route == Routes.register)) {
      return const RouteSettings(name: Routes.navBarView);
    }

    // Allow access to the requested route
    return null;
  }

  /// Called when middleware is triggered
  /// Can be used for logging or additional authentication checks
  @override
  GetPage? onPageCalled(GetPage? page) {
    debugPrint('AuthMiddleware: Accessing route ${page?.name}');
    return super.onPageCalled(page);
  }

  /// Called when building the page
  /// Can be used for additional security checks or user role validation
  @override
  Widget onPageBuilt(Widget page) {
    debugPrint('AuthMiddleware: Building page');
    return page;
  }
}
