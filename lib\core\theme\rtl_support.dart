import 'package:flutter/material.dart';

/// RTL (Right-to-Left) support utilities for Arabic language
/// Provides helpers for directional layouts and text alignment
class RTLSupport {
  RTLSupport._();

  /// Check if the current locale is RTL
  static bool isRTL(BuildContext context) {
    return Directionality.of(context) == TextDirection.rtl;
  }

  /// Get text direction based on locale
  static TextDirection getTextDirection(Locale locale) {
    // Arabic and other RTL languages
    const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
    return rtlLanguages.contains(locale.languageCode) 
        ? TextDirection.rtl 
        : TextDirection.ltr;
  }

  /// Get text alignment based on text direction
  static TextAlign getTextAlign(BuildContext context, {TextAlign? fallback}) {
    if (isRTL(context)) {
      return fallback == TextAlign.left ? TextAlign.right : 
             fallback == TextAlign.right ? TextAlign.left :
             fallback ?? TextAlign.right;
    }
    return fallback ?? TextAlign.left;
  }

  /// Get edge insets with proper RTL support
  static EdgeInsetsDirectional getDirectionalPadding({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  /// Get margin with proper RTL support
  static EdgeInsetsDirectional getDirectionalMargin({
    double start = 0,
    double top = 0,
    double end = 0,
    double bottom = 0,
  }) {
    return EdgeInsetsDirectional.fromSTEB(start, top, end, bottom);
  }

  /// Get border radius with RTL support
  static BorderRadiusDirectional getDirectionalBorderRadius({
    double topStart = 0,
    double topEnd = 0,
    double bottomStart = 0,
    double bottomEnd = 0,
  }) {
    return BorderRadiusDirectional.only(
      topStart: Radius.circular(topStart),
      topEnd: Radius.circular(topEnd),
      bottomStart: Radius.circular(bottomStart),
      bottomEnd: Radius.circular(bottomEnd),
    );
  }

  /// Get icon for back navigation based on text direction
  static IconData getBackIcon(BuildContext context) {
    return isRTL(context) 
        ? Icons.arrow_forward_ios 
        : Icons.arrow_back_ios;
  }

  /// Get icon for forward navigation based on text direction
  static IconData getForwardIcon(BuildContext context) {
    return isRTL(context) 
        ? Icons.arrow_back_ios 
        : Icons.arrow_forward_ios;
  }

  /// Wrap widget with proper directionality
  static Widget wrapWithDirectionality({
    required Widget child,
    required Locale locale,
  }) {
    return Directionality(
      textDirection: getTextDirection(locale),
      child: child,
    );
  }

  /// Get alignment based on text direction
  static Alignment getStartAlignment(BuildContext context) {
    return isRTL(context) ? Alignment.centerRight : Alignment.centerLeft;
  }

  /// Get alignment based on text direction
  static Alignment getEndAlignment(BuildContext context) {
    return isRTL(context) ? Alignment.centerLeft : Alignment.centerRight;
  }

  /// Get cross axis alignment for RTL support
  static CrossAxisAlignment getStartCrossAxisAlignment(BuildContext context) {
    return isRTL(context) 
        ? CrossAxisAlignment.end 
        : CrossAxisAlignment.start;
  }

  /// Get cross axis alignment for RTL support
  static CrossAxisAlignment getEndCrossAxisAlignment(BuildContext context) {
    return isRTL(context) 
        ? CrossAxisAlignment.start 
        : CrossAxisAlignment.end;
  }

  /// Get main axis alignment for RTL support
  static MainAxisAlignment getStartMainAxisAlignment(BuildContext context) {
    return isRTL(context) 
        ? MainAxisAlignment.end 
        : MainAxisAlignment.start;
  }

  /// Get main axis alignment for RTL support
  static MainAxisAlignment getEndMainAxisAlignment(BuildContext context) {
    return isRTL(context) 
        ? MainAxisAlignment.start 
        : MainAxisAlignment.end;
  }

  /// Get positioned widget values for RTL support
  static Map<String, double?> getPositioned({
    double? start,
    double? end,
    double? top,
    double? bottom,
    required BuildContext context,
  }) {
    if (isRTL(context)) {
      return {
        'right': start,
        'left': end,
        'top': top,
        'bottom': bottom,
      };
    } else {
      return {
        'left': start,
        'right': end,
        'top': top,
        'bottom': bottom,
      };
    }
  }
}
