import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/routes/app_pages.dart';

/// Root application widget that configures the entire app
/// Sets up responsive design, localization, routing, and global app settings
class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    // Initialize responsive design system with base screen dimensions (360x800)
    return ScreenUtilInit(
      designSize: const Size(360, 800),

      // Configure GetX material app with Arabic locale and navigation
      builder: ((context, child) => GetMaterialApp(
            // Set Arabic as default locale for RTL support
            locale: const Locale('ar'),
            title: 'Ma3ak App',
            debugShowCheckedModeBanner: false,

            // Set initial route and configure page routing system
            initialRoute: AppPages.initial,
            getPages: AppPages.pages,
          )),
    );
  }
}
