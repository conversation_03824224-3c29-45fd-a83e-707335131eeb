import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart'; // Ensure this contains the fetchAds method

/// Controller for managing advertisements display and carousel functionality
/// Handles ads data fetching, loading states, and carousel navigation indicators
class AdsController extends GetxController {
  // Observable state variables for ads management
  final isLoading = false.obs;        // Loading state indicator for API calls
  final adData = AdModel().obs;       // Container for advertisements data
  final dotIndex = 0.obs;             // Current carousel dot indicator index

  /// Updates the current dot indicator for ads carousel
  /// Called when user swipes or auto-scrolls through advertisement slides
  void handleAdsDots(int index) {
    dotIndex.value = index;
  }

  /// Fetches advertisements from API and updates local state
  /// Shows loading indicator during API call and handles success/error responses
  Future<void> fetchAds() async {
    // Set loading state during API call
    isLoading(true);

    // Call API to fetch advertisements data
    final data = await RemoteServicesImpl().fetchAds();
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString()); // Log error if fetch fails
    }, (dataModel) {
      // Store fetched ads data and log success
      adData.value = dataModel;
      debugPrint(adData.value.toString());
    });
  }

  /// Initializes controller by fetching ads data on startup
  /// Automatically loads advertisements when controller is created
  @override
  void onInit()  {
    fetchAds(); // Load ads data immediately
    super.onInit();
  }
}
