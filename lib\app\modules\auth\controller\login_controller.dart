import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/core/utils/secure_storage_utils.dart';

/// Controller for managing login form state and interactions
class LoginController extends GetxController {
  // Observable for password field visibility toggle
  var isPasswordVisible = false.obs;
  
  // Observable for remember me checkbox state
  var isRememberMeChecked = false.obs;

  // Observable for login loading state
  var isLoading = false.obs;

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  /// Toggles password visibility between hidden and visible
  void togglePasswordVisibility() {
    isPasswordVisible.value = !isPasswordVisible.value;
  }

  /// Toggles remember me checkbox state and updates storage
  void toggleRememberMe() {
    isRememberMeChecked.value = !isRememberMeChecked.value;

    // If unchecking Remember Me, clear saved credentials
    if (!isRememberMeChecked.value) {
      SecureStorageUtils.clearCredentials();
    }
  }

  /// Loads saved credentials and auto-fills the form if Remember Me is enabled
  /// Called during controller initialization to restore previous session
  void loadSavedCredentials() {
    final credentials = SecureStorageUtils.getSavedCredentials();

    if (credentials['rememberMe'] == true) {
      // Update Remember Me checkbox state
      isRememberMeChecked.value = true;

      // Auto-fill credentials will be handled by AuthController
      // since it owns the text controllers
    } else {
      isRememberMeChecked.value = false;
    }
  }

  /// Saves current credentials if Remember Me is enabled
  /// Called after successful login to persist user credentials
  Future<void> saveCredentialsIfEnabled(String email, String password) async {
    await SecureStorageUtils.saveCredentials(
      email: email,
      password: password,
      rememberMe: isRememberMeChecked.value,
    );
  }

  /// Checks if there are saved credentials available
  /// Returns true if Remember Me is enabled and credentials exist
  bool hasSavedCredentials() {
    return SecureStorageUtils.hasSavedCredentials();
  }

  /// Gets saved credentials for auto-fill
  /// Returns map with email, password, and rememberMe status
  Map<String, dynamic> getSavedCredentials() {
    return SecureStorageUtils.getSavedCredentials();
  }

  @override
  void onInit() {
    super.onInit();
    // Load saved credentials when controller initializes
    loadSavedCredentials();
  }
}
