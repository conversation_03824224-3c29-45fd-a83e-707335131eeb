import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/core/theme/design_tokens.dart';
import 'package:ma3ak/core/utils/accessibility_utils.dart';

/// Utility class for showing standardized dialogs throughout the app
/// Provides consistent dialog styling, animations, and RTL support
class DialogUtils {
  DialogUtils._();

  /// Shows a confirmation dialog with customizable title, message, and actions
  /// 
  /// Parameters:
  /// - [title]: Dialog title text (required)
  /// - [message]: Dialog content message (required)
  /// - [confirmText]: Text for confirm button (defaults to "نعم")
  /// - [cancelText]: Text for cancel button (defaults to "إلغاء")
  /// - [onConfirm]: Callback when confirm button is pressed
  /// - [onCancel]: Callback when cancel button is pressed (optional)
  /// - [confirmColor]: Color for confirm button (defaults to red for destructive actions)
  /// - [barrierDismissible]: Whether dialog can be dismissed by tapping outside (defaults to true)
  /// 
  /// Returns: Future<bool?> - true if confirmed, false if cancelled, null if dismissed
  static Future<bool?> showConfirmationDialog({
    required String title,
    required String message,
    String confirmText = 'نعم',
    String cancelText = 'إلغاء',
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
    Color? confirmColor,
    bool barrierDismissible = true,
  }) async {
    return await Get.dialog<bool>(
      // Custom animated dialog with scale transition
      AnimatedContainer(
        duration: DesignTokens.animationNormal,
        child: Dialog(
          backgroundColor: Colors.transparent,
          elevation: 0,
          child: TweenAnimationBuilder<double>(
            duration: DesignTokens.animationNormal,
            tween: Tween(begin: 0.0, end: 1.0),
            curve: Curves.easeOutBack,
            builder: (context, value, child) {
              return Transform.scale(
                scale: value,
                child: Opacity(
                  opacity: value,
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16),
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: AppColor.whiteColor,
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: AppColor.blackColor.withValues(alpha: 0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Dialog Title
                        Text(
                          title,
                          style: TextStyle(
                            fontSize: 18.sp,
                            fontWeight: FontWeight.w600,
                            color: AppColor.titleTextColor,
                          ),
                          textAlign: TextAlign.center,
                          semanticsLabel: AccessibilityUtils.getDialogSemantics(
                            title: title,
                            type: 'confirmation',
                          ),
                        ),
                        
                        const SizedBox(height: 16),
                        
                        // Dialog Message
                        Text(
                          message,
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: AppColor.textColor,
                            height: 1.4,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        
                        const SizedBox(height: 24),
                        
                        // Action Buttons Row
                        Row(
                          children: [
                            // Cancel Button
                            Expanded(
                              child: _buildDialogButton(
                                text: cancelText,
                                onPressed: () {
                                  onCancel?.call();
                                  Get.back(result: false);
                                },
                                isPrimary: false,
                              ),
                            ),
                            
                            const SizedBox(width: 16),
                            
                            // Confirm Button
                            Expanded(
                              child: _buildDialogButton(
                                text: confirmText,
                                onPressed: () {
                                  onConfirm?.call();
                                  Get.back(result: true);
                                },
                                isPrimary: true,
                                backgroundColor: confirmColor ?? AppColor.redColor,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
      barrierDismissible: barrierDismissible,
      barrierColor: AppColor.blackColor.withValues(alpha: 0.5),
    );
  }

  /// Builds a styled button for dialog actions
  /// 
  /// Parameters:
  /// - [text]: Button text
  /// - [onPressed]: Button press callback
  /// - [isPrimary]: Whether this is the primary action button
  /// - [backgroundColor]: Custom background color (optional)
  static Widget _buildDialogButton({
    required String text,
    required VoidCallback onPressed,
    required bool isPrimary,
    Color? backgroundColor,
  }) {
    return SizedBox(
      height: 44.h,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: isPrimary 
              ? (backgroundColor ?? AppColor.primaryColor)
              : AppColor.whiteColor,
          foregroundColor: isPrimary 
              ? AppColor.whiteColor 
              : AppColor.textColor,
          elevation: isPrimary ? 2 : 0,
          side: isPrimary 
              ? null 
              : BorderSide(
                  color: AppColor.textColor.withValues(alpha: 0.3),
                  width: 1,
                ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: Text(
          text,
          style: TextStyle(
            fontSize: 14.sp,
            fontWeight: FontWeight.w600,
          ),
          semanticsLabel: AccessibilityUtils.getButtonSemantics(
            label: text,
            hint: isPrimary ? 'تأكيد الإجراء' : 'إلغاء الإجراء',
          ),
        ),
      ),
    );
  }

  /// Shows a logout confirmation dialog specifically
  /// Pre-configured with appropriate texts and styling for logout action
  /// 
  /// Returns: Future<bool?> - true if user confirms logout, false if cancelled
  static Future<bool?> showLogoutConfirmationDialog() async {
    return await showConfirmationDialog(
      title: 'تأكيد تسجيل الخروج',
      message: 'هل أنت متأكد من أنك تريد تسجيل الخروج من حسابك؟',
      confirmText: 'نعم',
      cancelText: 'إلغاء',
      confirmColor: AppColor.redColor,
      barrierDismissible: true,
    );
  }
}
