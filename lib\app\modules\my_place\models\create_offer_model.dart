class CreateOfferModel {
  bool? status;
  String? message;
  Data? data;

  CreateOfferModel({this.status, this.message, this.data});

  CreateOfferModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

class Data {
  String? title;
  String? des;
  String? type;
  String? img;
  String? slug;
  int? ownerId;
  int? placeId;
  String? updatedAt;
  String? createdAt;
  int? id;

  Data(
      {this.title,
      this.des,
      this.type,
      this.img,
      this.slug,
      this.ownerId,
      this.placeId,
      this.updatedAt,
      this.createdAt,
      this.id});

  Data.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    des = json['des'];
    type = json['type'];
    img = json['img'];
    slug = json['slug'];
    ownerId = json['owner_id'];
    placeId = json['place_id'];
    updatedAt = json['updated_at'];
    createdAt = json['created_at'];
    id = json['id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['title'] = title;
    data['des'] = des;
    data['type'] = type;
    data['img'] = img;
    data['slug'] = slug;
    data['owner_id'] = ownerId;
    data['place_id'] = placeId;
    data['updated_at'] = updatedAt;
    data['created_at'] = createdAt;
    data['id'] = id;
    return data;
  }
}
