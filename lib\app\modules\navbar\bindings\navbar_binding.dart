import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/send_reply_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/send_commen_controller.dart';
import 'package:ma3ak/app/modules/place/controller/comments_controller.dart';
import 'package:ma3ak/app/modules/place/controller/greater_rate_place_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/app/modules/search/controller/search_controllers.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/data/api/api_server.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

import '../controller/navbar_controller.dart';

/// Dependency injection bindings for navbar and related controllers
/// Registers all controllers and services needed for main navigation and app functionality
class NavbarBinding extends Bindings {
  @override
  void dependencies() {
    // Core services and API layer - foundational services for data operations
    Get.lazyPut(() => ApiServer());
    Get.lazyPut(() => RemoteServicesImpl());

    // UI state controllers - shared across multiple features
    Get.lazyPut(() => ReactiveButtonController());

    // Navigation and authentication controllers - user session management
    Get.lazyPut(() => NavbarController());
    Get.lazyPut(() => AuthController());

    // Profile management controllers
    Get.lazyPut(() => ProfileController());

    // Content and interaction controllers - main app features
    Get.lazyPut(() => AdsController());                    // Advertisement display
    Get.lazyPut(() => PlaceController());                  // Place management
    Get.lazyPut(() => GreaterRatePlaceController());       // Top-rated places
    Get.lazyPut(() => OffersController());                 // Offers and deals
    Get.lazyPut(() => FavoriteController());               // User favorites
    Get.lazyPut(() => CommentsController());               // Comments system
    Get.lazyPut(() => SendCommentController());            // Comment submission
    Get.lazyPut(() => SendReplyController());              // Reply functionality
    Get.lazyPut(() => SearchPlaceController());            // Search operations
  }
}
