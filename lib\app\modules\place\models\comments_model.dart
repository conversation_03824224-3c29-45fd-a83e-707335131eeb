import 'dart:convert';

// To parse this JSON data, do
//
//     final commentsModel = commentsModelFromJson(jsonString);

CommentsModel commentsModelFromJson(String str) =>
    CommentsModel.fromJson(json.decode(str));

String commentsModelToJson(CommentsModel data) => json.encode(data.toJson());

class CommentsModel {
  final bool? status;
  final String? message;
  final List<PlaceData>? data;

  CommentsModel({
    this.status,
    this.message,
    this.data,
  });

  factory CommentsModel.fromJson(Map<String, dynamic> json) => CommentsModel(
        status: json["status"],
        message: json["message"],
        data: List<PlaceData>.from(
            json["data"].map((x) => PlaceData.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class PlaceData {
  final int id;
  final String name;
  final String governor;
  final String directorate;
  final String addressDetails;
  final int whatsappNumber;
  final String coverImage;
  final String slug;
  final String status;
  final List<Comment> comment;

  PlaceData({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.status,
    required this.comment,
  });

  factory PlaceData.fromJson(Map<String, dynamic> json) => PlaceData(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        status: json["status"],
        comment:
            List<Comment>.from(json["comment"].map((x) => Comment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "status": status,
        "comment": List<dynamic>.from(comment.map((x) => x.toJson())),
      };
}

class Comment {
  final int id;
  final String commentText;
  final int placeId;
  final int userId;
  final int? parentId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User user;
  final Reply? replies;

  Comment({
    required this.id,
    required this.commentText,
    required this.placeId,
    required this.userId,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
    this.replies,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json["id"],
        commentText: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        user: User.fromJson(json["user"]),
        replies:
            json["replies"] != null ? Reply.fromJson(json["replies"]) : null,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": commentText,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "user": user.toJson(),
        "replies": replies?.toJson(),
      };
}

class User {
  final int id;
  final String name;

  User({
    required this.id,
    required this.name,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"],
        name: json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
      };
}

class Reply {
  final int id;
  final String commentText;
  final int placeId;
  final int userId;
  final int? parentId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final User user;

  Reply({
    required this.id,
    required this.commentText,
    required this.placeId,
    required this.userId,
    this.parentId,
    required this.createdAt,
    required this.updatedAt,
    required this.user,
  });

  factory Reply.fromJson(Map<String, dynamic> json) => Reply(
        id: json["id"],
        commentText: json["comment"],
        placeId: json["place_id"],
        userId: json["user_id"],
        parentId: json["parent_id"],
        createdAt: DateTime.parse(json["created_at"]),
        updatedAt: DateTime.parse(json["updated_at"]),
        user: User.fromJson(json["user"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "comment": commentText,
        "place_id": placeId,
        "user_id": userId,
        "parent_id": parentId,
        "created_at": createdAt.toIso8601String(),
        "updated_at": updatedAt.toIso8601String(),
        "user": user.toJson(),
      };
}
