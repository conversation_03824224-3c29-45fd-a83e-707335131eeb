import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/models/owner_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_model.dart';
import 'package:ma3ak/app/modules/auth/models/user_registeration_model.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/core/utils/secure_storage_utils.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:ma3ak/main.dart';

/// Controller for handling user authentication (login/register/logout)
/// Manages authentication state, form inputs, and API calls for user sessions
class AuthController extends GetxController {
  // Form input controllers for authentication fields
  TextEditingController nameController = TextEditingController();           // User name input
  TextEditingController emailController = TextEditingController();          // User email input
  TextEditingController emailOwnerController = TextEditingController();     // Owner email input
  TextEditingController phoneController = TextEditingController();          // Phone number input
  TextEditingController passwordController = TextEditingController();       // User password input
  TextEditingController passwordOwnerController = TextEditingController();  // Owner password input
  TextEditingController confirmPasswordController = TextEditingController(); // Password confirmation

  // Observable state variables for authentication
  final isLoading = false.obs;                                              // Loading state indicator
  final userData = UserModel().obs;                                         // Current user data
  final ownerData = OwnerModel().obs;                                       // Current owner data
  final Rx<UserRegistrationModel> userRegisterationModel =                 // Registration response data
      UserRegistrationModel().obs;

  /// Saves user authentication token to local storage
  /// Stores the token for persistent authentication across app sessions
  void saveUserData(String? token) {
    box.write('token', token);
  }

  /// Loads saved credentials and auto-fills the login form
  /// Called when the login screen initializes to restore previous session
  void loadSavedCredentials() {
    final credentials = SecureStorageUtils.getSavedCredentials();

    if (credentials['rememberMe'] == true) {
      // Auto-fill email and password fields
      emailController.text = credentials['email'] ?? '';
      passwordController.text = credentials['password'] ?? '';
    }
  }

  /// Saves credentials after successful login if Remember Me is enabled
  /// Called from the login method to persist user credentials
  Future<void> saveCredentialsIfRememberMe(bool rememberMe) async {
    if (rememberMe) {
      await SecureStorageUtils.saveCredentials(
        email: emailController.text,
        password: passwordController.text,
        rememberMe: rememberMe,
      );
    }
  }

  /// Signs out user by clearing stored data and navigating to main screen
  /// Removes authentication token and user type, then redirects to navbar
  Future<void>? signOut() {
    // Clear authentication data from local storage
    box.remove('token');
    box.remove('isOwner');

    // Navigate to main screen after logout
    Get.offAll(() =>  const NavBarView());
    return null;
  }

  /// Registers a new user with provided credentials
  /// Creates user account and automatically logs them in on success
  Future<Either?> registerUser(
      String phoneNumber, String name, String email, String password) async {
    // Set loading state during registration process
    isLoading(true);

    // Call API to register new user
    final response = await RemoteServicesImpl()
        .registerUser(phoneNumber, name, email, password);
    isLoading(false);

    // Handle registration response using Either pattern
    response.fold(
      (l) => Get.snackbar("Error", l), // Show error message on failure
      (userRegisterModel) {
        // Store registration data and navigate to main app
        userRegisterationModel.value = userRegisterModel;
        Get.offAndToNamed(Routes.navBarView);

        // Show success message to user
        Get.snackbar(
          "Success",
          "تم تسجيل الدخول بنجاح",
        );
      },
    );
    return null;
  }

  /// Authenticates user with email and password
  /// Handles login for both regular users and business owners
  /// Optionally saves credentials if Remember Me is enabled
  Future<void> loginUser(String email, String password, {bool rememberMe = false}) async {
    // Set loading states for UI feedback
    isLoading(true);

    // Call API to authenticate user credentials
    final response = await RemoteServicesImpl().loginUser(email, password);
    isLoading(false);
    Get.find<ReactiveButtonController>().isLoading.value = false;

    // Handle login response using Either pattern
    response.fold((l) => Get.snackbar("Error", l), (userOrOwnerModel) async {
      // Store user data based on account type (owner vs regular user)
      if (box.read('isOwner') == 1) {
        ownerData.value = userOrOwnerModel;  // Store as owner data
      } else {
        userData.value = userOrOwnerModel;   // Store as regular user data
      }

      // Save credentials if Remember Me is enabled
      if (rememberMe) {
        await saveCredentialsIfRememberMe(rememberMe);
      }

      // Navigate to main app screen after successful login
      Get.offAndToNamed(Routes.navBarView);

      // Show success message to user
      Get.snackbar(
        "Success",
        "تم تسجيل الدخول بنجاح",
      );
    });

    // Ensure button loading state is reset
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }
}
