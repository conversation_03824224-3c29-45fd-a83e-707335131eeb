import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for managing highest-rated places data and operations
class GreaterRatePlaceController extends GetxController{
  // Loading state indicator for API requests
  var isLoading = false.obs;

  // Observable container for highest-rated places data
  var greaterRatePlaceData = PlaceModel().obs;

  /// Fetches places with the highest ratings from the API
  Future<void> fetchGreaterRatePlace() async {
    isLoading(true);

    // Call API service to retrieve highest-rated places
    final data = await RemoteServicesImpl()
        .fetchGreaterRatePlace();
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      greaterRatePlaceData.value = dataModel;
      debugPrint(greaterRatePlaceData.value.toString());
    });
  }

  /// Initialize controller by fetching highest-rated places on startup
  @override
  void onInit() {
    fetchGreaterRatePlace();
    super.onInit();
  }
}