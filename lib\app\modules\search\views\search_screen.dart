import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/search_text_form_field.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/models/ctegoreis_model.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/app/modules/search/controller/search_controllers.dart';
import 'package:ma3ak/app/modules/search/widgets/list_view_search_widget.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/card_widget.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';
import 'package:ma3ak/widgets/widgets/filter_options_widget.dart';

// Search screen with filtering and place discovery functionality
class SearchScreen extends StatelessWidget {
  SearchScreen({super.key, this.showBack = false});
  final bool? showBack; // Controls back button visibility

  // Controllers for search and place management
  final PlaceController placeController = Get.put(PlaceController());
  final SearchPlaceController searchPlaceController =
      Get.put(SearchPlaceController());
  // Builds the search screen with filters and results
  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      // Pull-to-refresh functionality for updating data
      onRefresh: () async {
        await placeController.fetchPlaces();
        await placeController.fetchSections();
      },
      child: SafeArea(
        child: Column(
          children: [
            const MainAppBar(),
            // Search input field with dynamic controller
            SearchTextFormField(
              searchController:
                  // searchPlaceController.sectionsData.value.data == null &&
                  searchPlaceController.isClearSections.value
                      ? placeController.searchPlaceController
                      : searchPlaceController.searchController,
              onChange: (text) {
                // searchPlaceController.sectionsData.value.data == null &&9
                searchPlaceController.isClearSections.value
                    ? placeController.searchPlaces(text)
                    : searchPlaceController.searchPlaces(text);
              },
            ),
            FilterOptionsWidget(),
            SizedBox(height: 10.h),
            Expanded(child: Obx(() {
              // final categoriesCards =
              //     searchPlaceController.categoriesData?.value.data;
              RxList<Place>? allPlaces = searchPlaceController.resultAllPlaces;

              if (placeController.resultPlaceList!.isNotEmpty &&
                  searchPlaceController.isClearSections.isTrue) {
                return ListViewSearchWidget(
                  isNeverScrollable: false,
                );
              }

              if (searchPlaceController.isLoading.value) {
                return const Center(child: CircularProgressIndicator());
              }

              if (searchPlaceController.sectionsData.value.data != null &&
                  allPlaces!.isNotEmpty) {
                return ListView.separated(
                  shrinkWrap: true,
                  physics: const AlwaysScrollableScrollPhysics(),
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  itemCount: allPlaces.length,
                  itemBuilder: (context, index) {
                    final placeCard = allPlaces[index];
                    return InkWell(
                      onTap: () async {
                         Get.dialog(
  Center(
    child: Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
      color: AppColor.primaryColor
      ),
      height: 100.h, // Adjust height to your desired value
      width: 100.w,   // Adjust width to your desired value
      child: const Center(child: CircularProgressIndicator(color: Colors.white,),),
    ),
  ),
  barrierColor: Colors.transparent,
);
                        var res = await placeController.getPlaceInforamtion(
                            slugPlace: placeCard.slug);

                        res.fold((error) {
                          // Dismiss the loading dialog after data is fetched
                          Get.back();
                          debugPrint(error.toString());
                        }, (dataModel) async {
                          await Get.find<OffersController>()
                              .fetchOffersByPlace(slugPlace: placeCard.slug);
                          placeController.placeInfromationOld.value = dataModel;
                          // Dismiss the loading dialog after data is fetched
                          Get.back();
                          if (box.read('isOwner') == 1) {
                            Get.to(() => PlaceScreenOwner(
                                placeInfromation:
                                    placeController.placeInfromationOld));
                          } else {
                            Get.to(() => PlaceScreen(
                                placeInfromation:
                                    placeController.placeInfromationOld));
                          }
                        });
                      },
                      child: CardWidget(
                        rating: placeCard.stars.toDouble(),
                        title: placeCard.name,
                        imgUrl: placeCard.coverImage,
                        isFave: null,
                        onPressed: () {
                         if (box.read('token') != null && box.read('isOwner') != 1) {
                    Get.find<FavoriteController>()
                        .addOrRemoveFavorite(slugPlace: placeCard.slug);
                  } else {
                    Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا',
                        backgroundColor: Colors.green,
                        colorText: Colors.white,
                        icon: const Icon(Icons.warning, color: Colors.white));
                  }
                        },
                        governor: placeCard.governor,
                        directorate: placeCard.directorate,
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox();
                  },
                );
              } else if (searchPlaceController.sectionsData.value.data ==
                      null ||
                  allPlaces!.isEmpty) {
                return Center(
                    child: Text(
                  'لا توجد أماكن في هذا القسم',
                  style: TextStyle(fontSize: 20.sp),
                ));
              }
              return Container();
            }))
          ],
        ),
      ),
    );
  }
}
