import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// Design tokens for consistent spacing, sizing, and layout across the app
/// Provides responsive values that adapt to different screen sizes
class DesignTokens {
  DesignTokens._();

  // SPACING TOKENS
  /// Extra small spacing - 4dp
  static double get spaceXS => 4.w;
  
  /// Small spacing - 8dp
  static double get spaceSM => 8.w;
  
  /// Medium spacing - 16dp
  static double get spaceMD => 16.w;
  
  /// Large spacing - 24dp
  static double get spaceLG => 24.w;
  
  /// Extra large spacing - 32dp
  static double get spaceXL => 32.w;
  
  /// Extra extra large spacing - 48dp
  static double get spaceXXL => 48.w;

  // BORDER RADIUS TOKENS
  /// Small border radius - 8dp
  static double get radiusSM => 8.r;
  
  /// Medium border radius - 12dp
  static double get radiusMD => 12.r;
  
  /// Large border radius - 16dp
  static double get radiusLG => 16.r;
  
  /// Extra large border radius - 24dp
  static double get radiusXL => 24.r;
  
  /// Circular border radius - 50dp
  static double get radiusCircular => 50.r;

  // ELEVATION TOKENS
  /// No elevation
  static const double elevationNone = 0;
  
  /// Small elevation - 2dp
  static const double elevationSM = 2;
  
  /// Medium elevation - 4dp
  static const double elevationMD = 4;
  
  /// Large elevation - 8dp
  static const double elevationLG = 8;
  
  /// Extra large elevation - 16dp
  static const double elevationXL = 16;

  // ICON SIZE TOKENS
  /// Small icon size - 16dp
  static double get iconSM => 16.w;
  
  /// Medium icon size - 24dp
  static double get iconMD => 24.w;
  
  /// Large icon size - 32dp
  static double get iconLG => 32.w;
  
  /// Extra large icon size - 48dp
  static double get iconXL => 48.w;

  // BUTTON HEIGHT TOKENS
  /// Small button height - 32dp
  static double get buttonHeightSM => 32.h;
  
  /// Medium button height - 48dp
  static double get buttonHeightMD => 48.h;
  
  /// Large button height - 56dp
  static double get buttonHeightLG => 56.h;

  // CONTAINER SIZE TOKENS
  /// App bar height
  static double get appBarHeight => 80.h;
  
  /// Bottom navigation height
  static double get bottomNavHeight => 60.h;
  
  /// Floating action button size
  static double get fabSize => 65.r;
  
  /// Card minimum height
  static double get cardMinHeight => 120.h;

  // RESPONSIVE BREAKPOINTS
  /// Mobile breakpoint
  static const double mobileBreakpoint = 480;
  
  /// Tablet breakpoint
  static const double tabletBreakpoint = 768;
  
  /// Desktop breakpoint
  static const double desktopBreakpoint = 1024;

  // ANIMATION DURATIONS
  /// Fast animation duration
  static const Duration animationFast = Duration(milliseconds: 150);
  
  /// Normal animation duration
  static const Duration animationNormal = Duration(milliseconds: 300);
  
  /// Slow animation duration
  static const Duration animationSlow = Duration(milliseconds: 500);

  // RESPONSIVE HELPERS
  /// Check if current screen is mobile size
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobileBreakpoint;
  }
  
  /// Check if current screen is tablet size
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobileBreakpoint && width < desktopBreakpoint;
  }
  
  /// Check if current screen is desktop size
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktopBreakpoint;
  }

  /// Get responsive padding based on screen size
  static EdgeInsets getResponsivePadding(BuildContext context) {
    if (isDesktop(context)) {
      return EdgeInsets.symmetric(horizontal: spaceXXL, vertical: spaceLG);
    } else if (isTablet(context)) {
      return EdgeInsets.symmetric(horizontal: spaceXL, vertical: spaceMD);
    } else {
      return EdgeInsets.symmetric(horizontal: spaceMD, vertical: spaceSM);
    }
  }

  /// Get responsive margin based on screen size
  static EdgeInsets getResponsiveMargin(BuildContext context) {
    if (isDesktop(context)) {
      return EdgeInsets.all(spaceXL);
    } else if (isTablet(context)) {
      return EdgeInsets.all(spaceLG);
    } else {
      return EdgeInsets.all(spaceMD);
    }
  }

  /// Get responsive column count for grid layouts
  static int getResponsiveColumns(BuildContext context) {
    if (isDesktop(context)) {
      return 4;
    } else if (isTablet(context)) {
      return 3;
    } else {
      return 2;
    }
  }
}
