class PlaceInfromationOldModel {
  bool? status;
  String? message;
  Data? data;

  PlaceInfromationOldModel({this.status, this.message, this.data});

  PlaceInfromationOldModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;  
  }
}

class Data {
  int? id;
  String? name;
  String? governor;
  String? directorate;
  String? addressDetails;
  String? des;
  int? whatsappNumber;
  String? coverImage;
  String? slug;
  int? stars;
  String? status;
  int? parentId;
  int? catId;
  int? ownerId;
  List<Office>? office;
  List<Comments>? comments;

  Data(
      {this.id,
      this.name,
      this.governor,
      this.directorate,
      this.addressDetails,
      this.des,
      this.whatsappNumber,
      this.coverImage,
      this.slug,
      this.stars,
      this.status,
      this.parentId,
      this.catId,
      this.ownerId,
      this.office,
      this.comments});

  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    governor = json['governor'];
    directorate = json['directorate'];
    addressDetails = json['address_details'];
    des = json['des'];
    whatsappNumber = json['whatsapp_number'];
    coverImage = json['cover_image'];
    slug = json['slug'];
    stars = json['stars'];
    status = json['status'];
    parentId = json['parent_id'];
    catId = json['cat_id'];
    ownerId = json['owner_id'];
    if (json['office'] != null) {
      office = <Office>[];
      json['office'].forEach((v) {
        office!.add(Office.fromJson(v));
      });
    }
    if (json['comments'] != null) {
      comments = <Comments>[];
      json['comments'].forEach((v) {
        comments!.add(Comments.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['governor'] = governor;
    data['directorate'] = directorate;
    data['address_details'] = addressDetails;
    data['des'] = des;
    data['whatsapp_number'] = whatsappNumber;
    data['cover_image'] = coverImage;
    data['slug'] = slug;
    data['stars'] = stars;
    data['status'] = status;
    data['parent_id'] = parentId;
    data['cat_id'] = catId;
    data['owner_id'] = ownerId;
    if (office != null) {
      data['office'] = office!.map((v) => v.toJson()).toList();
    }
    if (comments != null) {
      data['comments'] = comments!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Office {
  int? id;
  String? name;
  String? governor;
  String? directorate;
  String? addressDetails;
  String? des;
  int? whatsappNumber;
  String? coverImage;
  String? slug;
  int? stars;
  String? status;
  int? parentId;
  int? catId;
  int? ownerId;

  Office(
      {this.id,
      this.name,
      this.governor,
      this.directorate,
      this.addressDetails,
      this.des,
      this.whatsappNumber,
      this.coverImage,
      this.slug,
      this.stars,
      this.status,
      this.parentId,
      this.catId,
      this.ownerId});

  Office.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    governor = json['governor'];
    directorate = json['directorate'];
    addressDetails = json['address_details'];
    des = json['des'];
    whatsappNumber = json['whatsapp_number'];
    coverImage = json['cover_image'];
    slug = json['slug'];
    stars = json['stars'];
    status = json['status'];
    parentId = json['parent_id'];
    catId = json['cat_id'];
    ownerId = json['owner_id'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['governor'] = governor;
    data['directorate'] = directorate;
    data['address_details'] = addressDetails;
    data['des'] = des;
    data['whatsapp_number'] = whatsappNumber;
    data['cover_image'] = coverImage;
    data['slug'] = slug;
    data['stars'] = stars;
    data['status'] = status;
    data['parent_id'] = parentId;
    data['cat_id'] = catId;
    data['owner_id'] = ownerId;
    return data;
  }
}

class Comments {
  int? id;
  String? comment;
  int? placeId;
  int? userId;
  int? parentId;
  String? createdAt;
  String? updatedAt;
  User? user;
  Replies? replies;

  Comments(
      {this.id,
      this.comment,
      this.placeId,
      this.userId,
      this.parentId,
      this.createdAt,
      this.updatedAt,
      this.user,
      this.replies});

  Comments.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    comment = json['comment'];
    placeId = json['place_id'];
    userId = json['user_id'];
    parentId = json['parent_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
    replies =
        json['replies'] != null ? Replies.fromJson(json['replies']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['comment'] = comment;
    data['place_id'] = placeId;
    data['user_id'] = userId;
    data['parent_id'] = parentId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    if (replies != null) {
      data['replies'] = replies!.toJson();
    }
    return data;
  }
}

class User {
  int? id;
  String? name;

  User({this.id, this.name});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    return data;
  }
}

class Replies {
  int? id;
  String? comment;
  int? placeId;
  int? userId;
  int? parentId;
  String? createdAt;
  String? updatedAt;
  User? user;

  Replies(
      {this.id,
      this.comment,
      this.placeId,
      this.userId,
      this.parentId,
      this.createdAt,
      this.updatedAt,
      this.user});

  Replies.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    comment = json['comment'];
    placeId = json['place_id'];
    userId = json['user_id'];
    parentId = json['parent_id'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
    user = json['user'] != null ? User.fromJson(json['user']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['comment'] = comment;
    data['place_id'] = placeId;
    data['user_id'] = userId;
    data['parent_id'] = parentId;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    if (user != null) {
      data['user'] = user!.toJson();
    }
    return data;
  }
}
