import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/widgets/basic_button.dart';

class ContainerDashboardOwnerInfo extends StatelessWidget {
  const ContainerDashboardOwnerInfo({
    super.key,
    this.name = ' لا يوجد اسم',
    this.city = 'لا يوجد مدينة',
    this.onPressed,
    this.url,
  });
  final String? name;
  final String? city;
  final void Function()? onPressed;
  final String? url;

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 16.w),
        height: MediaQuery.of(context).size.height * .2,
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.grey), // Border color
          borderRadius: BorderRadius.circular(8.r), // Circular border
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 35.r,
                      backgroundImage: url == null
                          ? null
                          : CachedNetworkImageProvider(ApiList.baseUrl + url!),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          name??'لا يوجد اسم',
                          style: const TextStyle(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          city??'لا يوجد مدينة',
                          style: const TextStyle(color: Colors.grey),
                        ),
                      ],
                    ),
                  ],
                ),
                BasicButton(
                    onPressed: onPressed,
                    wdth: MediaQuery.of(context).size.width * .35,
                    text: 'تحديث ')
              ],
            )
          ],
        ));
  }
}
