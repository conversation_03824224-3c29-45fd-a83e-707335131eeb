import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/dashboard/models/dashboard_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for managing dashboard data and state for business owners
class DashboardController extends GetxController {
  // Loading state indicator for UI feedback
  var isLoading = false.obs;
  
  // Observable dashboard data containing owner statistics
  Rx<DashboardModel> dashboardData = DashboardModel().obs;

  /// Fetches dashboard statistics from API
  /// Updates loading state and handles success/error responses
  Future<void> fetchDashboardOwner() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchDashboard(); // Make sure this method exists
    isLoading(false);
    
    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      dashboardData.value = dataModel;
      debugPrint(dashboardData.value.data.toString());
    });
  }

  // Initialize controller and fetch dashboard data on startup
  @override
  void onInit() {
    fetchDashboardOwner(); // Fetch dashboard data on initialization
    super.onInit();
  }
}
