// Data model for place update API response
class UpdatePlaceModel {
  bool? status;
  String? message;
  Data? data;

  UpdatePlaceModel({this.status, this.message, this.data});

  // Creates UpdatePlaceModel from JSON response
  UpdatePlaceModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    data = json['data'] != null ? Data.fromJson(json['data']) : null;
  }

  // Converts UpdatePlaceModel to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (this.data != null) {
      data['data'] = this.data!.toJson();
    }
    return data;
  }
}

// Data model for updated place information
class Data {
  int? id;
  String? name;
  String? governor;
  String? directorate;
  String? addressDetails;
  String? des;
  int? whatsappNumber;
  String? coverImage;
  String? slug;
  int? stars;
  String? status;
  Null parentId;
  int? catId;
  int? ownerId;

  Data(
      {this.id,
      this.name,
      this.governor,
      this.directorate,
      this.addressDetails,
      this.des,
      this.whatsappNumber,
      this.coverImage,
      this.slug,
      this.stars,
      this.status,
      this.parentId,
      this.catId,
      this.ownerId});

  // Creates Data from JSON response
  Data.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    governor = json['governor'];
    directorate = json['directorate'];
    addressDetails = json['address_details'];
    des = json['des'];
    whatsappNumber = json['whatsapp_number'];
    coverImage = json['cover_image'];
    slug = json['slug'];
    stars = json['stars'];
    status = json['status'];
    parentId = json['parent_id'];
    catId = json['cat_id'];
    ownerId = json['owner_id'];
  }

  // Converts Data to JSON format
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['governor'] = governor;
    data['directorate'] = directorate;
    data['address_details'] = addressDetails;
    data['des'] = des;
    data['whatsapp_number'] = whatsappNumber;
    data['cover_image'] = coverImage;
    data['slug'] = slug;
    data['stars'] = stars;
    data['status'] = status;
    data['parent_id'] = parentId;
    data['cat_id'] = catId;
    data['owner_id'] = ownerId;
    return data;
  }
}
