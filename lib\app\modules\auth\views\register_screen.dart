import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/auth/controller/register_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/core/utils/validation_utils.dart';
import 'package:ma3ak/core/widgets/loading_widgets.dart';
import 'package:ma3ak/app/modules/auth/widgets/text_form_field_auth.dart';

import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:ma3ak/widgets/widgets/basic_app_bar.dart';

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final registerController = Get.put(RegisterController());
    final authController = Get.put(AuthController());
    var wdth = MediaQuery.of(context).size.width;
    var hgt = MediaQuery.of(context).size.height;
    hgt;
    return Scaffold(
        backgroundColor: AppColor.grayColor,
        appBar: const BasicAppbar(
          title: Text('إنشاء حساب'),
        ),
        body: SingleChildScrollView(
            child: SizedBox(
          child: Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                width: double.infinity,
                // height: hgt * .80,
                decoration: BoxDecoration(
                  color: AppColor.whiteColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Form(
                  key: registerController.formKey,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Image.asset(
                        ImagesPic.logoM,
                        height: 100,
                        width: 100,
                      ),
                      const Text(
                        'أهلاً وسهلاً بك في معاك ',
                      ),
                      Column(
                        children: [
                          TextFormFieldAuth(
                            controller: authController.nameController,
                            labelText: 'الإسم',
                            prefixIcon: const Icon(Icons.person),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء إدخال الإسم';
                              }
                              return null; // No errors
                            },
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          TextFormFieldAuth(
                            controller: authController.emailController,
                            labelText: 'البريد الإلكتروني',
                            prefixIcon: const Icon(Icons.person),
                            validator: ValidationUtils.validateEmail,
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          TextFormFieldAuth(
                            controller: authController.phoneController,
                            labelText: 'رقم الهاتف',
                            prefixIcon: const Icon(Icons.person),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء إدخال رقم الهاتف';
                              }
                              if (value.length < 9) {
                                return 'رقم الهاتف يجب أن يكون 9 أرقام على الأقل';
                              }
                              return null; // No errors
                            },
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          Obx(
                            () => TextFormFieldAuth(
                              controller: authController.passwordController,
                              labelText: 'كلمة المرور',
                              prefixIcon: const Icon(Icons.person),
                              obscureText:
                                  !registerController.isPasswordVisible.value,
                              suffixIcon: IconButton(
                                icon: Icon(
                                  registerController.isPasswordVisible.value
                                      ? Icons.visibility
                                      : Icons.visibility_off,
                                ),
                                onPressed:
                                    registerController.togglePasswordVisibility,
                              ),
                              validator: ValidationUtils.validatePassword,
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          TextFormFieldAuth(
                            controller:
                                authController.confirmPasswordController,
                            labelText: 'كلمة المرور مره اخرى',
                            prefixIcon: const Icon(Icons.person),
                            obscureText: true,
                            validator: (value) => ValidationUtils.validatePasswordConfirmation(
                              authController.passwordController.text,
                              value,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Obx(() {
                        return LoadingWidgets.loadingButton(
                          text: 'تسجيل',
                          width: wdth * 0.7,
                          isLoading: registerController.isLoading.value,
                          onPressed: () async {
                            if (registerController.formKey.currentState!
                                .validate()) {
                              registerController.isLoading.value = true;
                              await authController.registerUser(
                                authController.phoneController.text,
                                authController.nameController.text,
                                authController.emailController.text,
                                authController.passwordController.text,
                              );
                              registerController.isLoading.value = false;
                            }
                          },
                        );
                      }), TextButton(
                        onPressed: () {
                          Get.offNamed(Routes.login);
                        },
                        child: const Text(
                          'لديك حساب !',
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        )));
  }
}
