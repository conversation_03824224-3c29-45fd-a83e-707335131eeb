import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offer_info/views/offer_info_screen.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/data/constants/api_list.dart';

class CurrentOffersInPlacePassAll extends StatelessWidget {
  CurrentOffersInPlacePassAll({
    super.key,
    required this.namePlace,
    required this.slugPlace,
  });
  final String namePlace;
  final String slugPlace;
  final OffersController offersController = Get.put(OffersController());

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      var offer = offersController.offerDataByPlace.value.data;

      if (offersController.offerDataByPlace.value.data!.isEmpty) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 10.sp),
            alignment: Alignment.center,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              color: Colors.grey[300],
            ),
            child: const Text(
              'لا يوجد عروض حالية',
              style: TextStyle(fontSize: 18),
            ),
          ),
        );
      }
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'العروض الحالية',
              style: TextStyle(fontSize: 20.sp, fontWeight: FontWeight.bold),
            ),
            const SizedBox(
              height: 10,
            ),
            GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                mainAxisSpacing: 8,
                crossAxisSpacing: 8,
              ),
              itemCount:
                  offer?.length ?? 0, // Replace with your actual item count
              itemBuilder: (context, index) {
                var offerCard = offer![index];
                // Your grid item widget here
                return InkWell(
                  onTap: () {
                    Get.to(
                      () => OfferInfoScreen(
                        disLikes: offerCard.dislikes,
                        likes: offerCard.likes,
                        slugPlace: slugPlace,
                        slugOffer: offerCard.slug,
                        namePlace: namePlace,
                        title: offerCard.title,
                        type: offerCard.type,
                        des: offerCard.des,
                        img: offerCard.img,
                      ),
                    );
                  },
                  child: Column(
                    children: [
                      Expanded(
                        child: Container(
                            color: Colors.grey[300],
                            child: CachedNetworkImage(
                              fit: BoxFit.cover,
                              imageUrl: ApiList.baseUrl + offerCard.img,
                              progressIndicatorBuilder:
                                  (context, url, progress) {
                                return const Center(
                                  child: CircularProgressIndicator(),
                                );
                              },
                              errorWidget: (context, url, error) {
                                return const Icon(Icons.error);
                              },
                            )),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Container(
                          padding: EdgeInsets.symmetric(
                              vertical: 2.sp, horizontal: 2.sp),
                          alignment: Alignment.center,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.only(
                              bottomLeft: Radius.circular(8.r),
                              bottomRight: Radius.circular(8.r),
                            ),
                          ),
                          child: Text(
                            offerCard.title,
                          ),
                        ),
                      )
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      );
    });
  }
}
