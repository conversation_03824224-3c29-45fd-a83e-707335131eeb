import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

class UpdateOfferController extends GetxController {
  var isLoading = false.obs;
  RxString slugOffer = ''.obs;

  var selectedImagePath = ''.obs;
  var types = ['وظائف', 'دورات تدريبية', 'مسابقات', 'خصومات', 'اخرى'].obs;

  // The selected type
  var selectedType = ''.obs;
  var oldType = ''.obs;

  TextEditingController nameController = TextEditingController();
  TextEditingController titleController = TextEditingController();
  TextEditingController typeController = TextEditingController();
  TextEditingController desController = TextEditingController();
  Rx<String> image = ''.obs; // Initialize with an empty string>

  final ImagePicker _imagePicker = ImagePicker();

  Future<void> pickOneImage() async {
    try {
      final pickedImage =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        selectedImagePath.value = pickedImage.path;
      } else {
        Get.snackbar('Error', 'No Image Selected');
      }
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }

  Future<MultipartFile?> convertFileToMultipartFile() async {
    if (selectedImagePath.isNotEmpty) {
      return MultipartFile(selectedImagePath.value,
          filename: selectedImagePath.value.split('/').last,
          contentType: "multipart/form-data");
    }
    return null;
  }

  Future<void> updateOffer({required String slugOffer}) async {
    final multipartFile = await convertFileToMultipartFile();
    final formData = FormData({
      'title': titleController.text,
      'type': typeController.text,
      'des': desController.text,
    });
    if (multipartFile == null) {
      // Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(MapEntry('img', multipartFile));
    }
    isLoading.value = true;
    final data = await RemoteServicesImpl().updateOffer(
        slugOffer: slugOffer, body: formData); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      // sendCommentModel?.value = dataModel;
      debugPrint(dataModel.toString());
    });
  }
}
