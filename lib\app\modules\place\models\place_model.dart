import 'dart:convert';

// JSON parsing utility functions for PlaceModel
PlaceModel placeModelFromJson(String str) => PlaceModel.fromJson(json.decode(str));

String placeModelToJson(PlaceModel data) => json.encode(data.toJson());

/// Model representing API response containing places data
class PlaceModel {
  // Response status indicating success/failure
  final bool? status;

  // Response message with additional information
  final String? message;

  // List of place data items
  final List<PlaceDatum>? data;

  PlaceModel({
     this.status,
     this.message,
     this.data,
  });

  // Creates PlaceModel from JSON response
  factory PlaceModel.fromJson(Map<String, dynamic> json) => PlaceModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<PlaceDatum>.from(json["data"].map((x) => PlaceDatum.fromJson(x))),
      );

  // Converts PlaceModel to JSON format
  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

/// Model representing individual place data
class PlaceDatum {
  // Unique place identifier
  final int id;

  // Place name/title
  final String name;

  // Governor/state location
  final String governor;

  // Directorate/district location
  final String directorate;

  // Detailed address information
  final String addressDetails;

  // Place description
  final String des;

  // WhatsApp contact number
  final int whatsappNumber;

  // Cover image URL
  final String coverImage;

  // URL slug for the place
  final String slug;

  // Star rating (1-5)
  final int stars;

  // Place status (active/inactive)
  final String status;

  // Parent place ID (optional for sub-places)
  final int? parentId;

  // Category ID
  final int catId;

  // Owner user ID
  final int ownerId;

  PlaceDatum({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
  });

  // Creates PlaceDatum from JSON data
  factory PlaceDatum.fromJson(Map<String, dynamic> json) => PlaceDatum(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
      );

  // Converts PlaceDatum to JSON format
  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
      };
}
