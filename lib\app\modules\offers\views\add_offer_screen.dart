import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/my_offers_controller.dart';
import 'package:ma3ak/app/modules/my_place/controller/my_place_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/widgets/text_form_field_offer_widget.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/widgets/reactive_button.dart';

class AddOfferScreen extends StatelessWidget {
  AddOfferScreen({super.key});
  final offersController = Get.find<OffersController>();
  final myPlaceController = Get.put<MyPlaceController>(MyPlaceController());
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Form(
              key: offersController.formKey,
              child: Column(
                children: [
                  TextFormFieldOfferWidget(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'ادخل اسم العرض';
                      }
                      return null;
                    },
                    controller: offersController.titleController,
                    labelText: 'اسم العرض',
                    hintText: 'ادخل اسم العرض',
                  ),
                  TextFormFieldOfferWidget(
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'ادخل اسم الوصف';
                      }
                      return null;
                    },
                    controller: offersController.desController,
                    labelText: 'اسم الوصف',
                    hintText: 'ادخل اسم الوصف',
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Obx(
                      () => DropdownButtonFormField<String>(
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'اختر نوع العرض';
                          }
                          return null;
                        },
                        value: offersController.selectedType.value.isEmpty
                            ? null
                            : offersController.selectedType.value,
                        decoration: InputDecoration(
                          contentPadding:
                              const EdgeInsets.symmetric(horizontal: 10),
                          labelText: 'اسم النوع',
                          hintText: 'اختر نوع العرض',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                        ),
                        onChanged: (String? newValue) {
                          offersController.selectedType.value = newValue!;
                        },
                        items: offersController.types
                            .map((type) => DropdownMenuItem<String>(
                                  value: type,
                                  child: Text(type),
                                ))
                            .toList(),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Obx(() {
                      var myPlace = myPlaceController.myPlaceData.value.data;
                      return DropdownButtonFormField<String>(
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'اختر المكان';
                            }
                            return null;
                          },
                          value: myPlaceController.selectedPlaceSlug.value == ''
                              ? null
                              : myPlaceController.selectedPlaceSlug.value,
                          decoration: InputDecoration(
                            contentPadding:
                                const EdgeInsets.symmetric(horizontal: 10),
                            labelText: 'اسم المكان',
                            hintText: 'اختر  المكان',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8.0),
                            ),
                          ),
                          onChanged: (String? newValue) {
                            myPlaceController.selectedPlaceSlug.value =
                                newValue!;
                          },
                          items: myPlace
                              ?.map((place) => DropdownMenuItem<String>(
                                    value: place.slug,
                                    child: Text(place.name),
                                  ))
                              .toList());
                    }),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Obx(
                    () => SizedBox(
                        child: offersController.selectedImagePath.value.isEmpty
                            ? const Padding(
                                padding: EdgeInsets.all(16.0),
                                child: Text('لا توجد صورة ,الرجاء اضافة صورة'),
                              )
                            : Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        .25,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.withOpacity(.5),
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                          image: FileImage(File(offersController
                                              .selectedImagePath.value)),
                                          fit: BoxFit.contain),
                                    )),
                              )),
                  )
                ],
              ),
            ),
            Column(
              children: [
                IconButton.outlined(
                  onPressed: () async {
                    await offersController.pickOneImage();
                  },
                  icon: const Icon(
                    size: 40,
                    Icons.add_photo_alternate,
                  ),
                ),
                const SizedBox(height: 10),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: ReactiveButton(
                      wdth: double.infinity,
                      text: 'حفظ',
                      onPressed: () async {
                        if (offersController.formKey.currentState!.validate() &&
                            offersController
                                .selectedImagePath.value.isNotEmpty) {
                          final reactiveButtonController =
                              Get.find<ReactiveButtonController>();
                          reactiveButtonController.isLoading.value = true;
                           await offersController.createOffer(
                            slugPlace:
                                myPlaceController.selectedPlaceSlug.value,
                          );
                          
                          Get.find<MyOffersController>().fetchMyOffersOffers();
                          reactiveButtonController.isLoading.value = false;
                        }

                        if (offersController.selectedImagePath.value.isEmpty) {
                          Get.snackbar(
                              backgroundColor: Colors.red,
                              colorText: Colors.white,
                              icon: const Icon(Icons.warning,
                                  color: Colors.white),
                              'Error',
                              'الرجاء اضافة صورة');
                        }
                      }),
                ),
                const SizedBox(
                  height: 40,
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
