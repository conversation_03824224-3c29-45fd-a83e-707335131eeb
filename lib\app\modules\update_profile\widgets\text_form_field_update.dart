import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';

class TextFormFieldUpdate extends StatelessWidget {
  const TextFormFieldUpdate({
    super.key,
    this.validator,
    this.controller,
    this.labelText,
    this.hintText,
    this.obscureText = false,
  });
  final String? Function(String?)? validator;
  final TextEditingController? controller;
  final String? labelText;
  final String? hintText;
  final bool? obscureText;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: TextFormField(
        obscureText: obscureText!,
        // initialValue: controller?.text,
        validator: validator,
        controller: controller,
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 10),
          labelText: labelText,
          hintText: hintText,
          enabledBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: AppColor.blueBorderColor),
          ),
          errorBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: AppColor.blueBorderColor),
          ),
          border: const OutlineInputBorder(
            borderSide: BorderSide(color: AppColor.blueBorderColor),
          ),
          focusedBorder: const OutlineInputBorder(
            borderSide: BorderSide(color: AppColor.blueBorderColor),
          ),
        ),
      ),
    );
  }
}
