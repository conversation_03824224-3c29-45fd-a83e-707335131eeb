import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/core/theme/text_theme.dart';
import 'package:ma3ak/core/theme/component_themes.dart';

/// Centralized theme configuration for the Ma3ak app
/// Provides comprehensive theming with RTL support and responsive design tokens
class AppTheme {
  AppTheme._();

  /// Light theme configuration for the application
  static ThemeData get lightTheme {
    return ThemeData(
      // Basic theme properties
      useMaterial3: true,
      brightness: Brightness.light,
      
      // Color scheme based on app colors
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColor.primaryColor,
        brightness: Brightness.light,
        primary: AppColor.primaryColor,
        secondary: AppColor.primaryColor1,
        surface: AppColor.primaryBackgroundColor,
        error: AppColor.error,
        onPrimary: AppColor.whiteColor,
        onSecondary: AppColor.textColor,
        onSurface: AppColor.textColor,
        onError: AppColor.whiteColor,
      ),

      // Primary color configuration
      primaryColor: AppColor.primaryColor,
      primaryColorLight: AppColor.primaryColor1,
      scaffoldBackgroundColor: AppColor.primaryBack,
      
      // Text theme configuration
      textTheme: AppTextTheme.textTheme,
      primaryTextTheme: AppTextTheme.primaryTextTheme,
      

      
      // Bottom navigation bar theme
      bottomNavigationBarTheme: ComponentThemes.bottomNavigationBarTheme,
      
      // Button themes
      elevatedButtonTheme: ComponentThemes.elevatedButtonTheme,
      textButtonTheme: ComponentThemes.textButtonTheme,
      outlinedButtonTheme: ComponentThemes.outlinedButtonTheme,
      
      // Input decoration theme
      inputDecorationTheme: ComponentThemes.inputDecorationTheme,
      
      // Card theme
      cardTheme: ComponentThemes.cardTheme,
      
      // Dialog theme
      dialogTheme: ComponentThemes.dialogTheme,
      
      // Snackbar theme
      snackBarTheme: ComponentThemes.snackBarTheme,
      
      // Floating action button theme
      floatingActionButtonTheme: ComponentThemes.floatingActionButtonTheme,
      
      // Bottom app bar theme
      bottomAppBarTheme: ComponentThemes.bottomAppBarTheme,
      
      // Divider theme
      dividerTheme: ComponentThemes.dividerTheme,
      
      // Icon theme
      iconTheme: ComponentThemes.iconTheme,
      primaryIconTheme: ComponentThemes.primaryIconTheme,
      
      // System UI overlay style
      appBarTheme: ComponentThemes.appBarTheme.copyWith(
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: Colors.transparent,
          statusBarIconBrightness: Brightness.dark,
          statusBarBrightness: Brightness.light,
          systemNavigationBarColor: AppColor.whiteColor,
          systemNavigationBarIconBrightness: Brightness.dark,
        ),
      ),
    );
  }

  /// Dark theme configuration (for future implementation)
  static ThemeData get darkTheme {
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: AppColor.primaryColor,
        brightness: Brightness.dark,
      ),
      primaryColor: AppColor.primaryColor,
      scaffoldBackgroundColor: AppColor.blackColor,
    );
  }
}
