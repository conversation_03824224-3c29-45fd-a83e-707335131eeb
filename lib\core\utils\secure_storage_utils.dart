import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:ma3ak/main.dart';

/// Utility class for secure storage of sensitive data like passwords
/// Provides encryption/decryption capabilities for credential storage
class SecureStorageUtils {
  SecureStorageUtils._();

  // Storage keys for Remember Me functionality
  static const String _savedEmailKey = 'saved_email';
  static const String _savedPasswordKey = 'saved_password';
  static const String _rememberMeEnabledKey = 'remember_me_enabled';
  static const String _encryptionKeyKey = 'encryption_key';

  /// Generates or retrieves the encryption key for password encryption
  /// Creates a new key if one doesn't exist, otherwise returns the stored key
  static String _getEncryptionKey() {
    String? existingKey = box.read(_encryptionKeyKey);
    if (existingKey != null) {
      return existingKey;
    }

    // Generate a new encryption key based on device-specific data
    final deviceId = DateTime.now().millisecondsSinceEpoch.toString();
    final key = sha256.convert(utf8.encode('ma3ak_app_$deviceId')).toString();
    
    // Store the key for future use
    box.write(_encryptionKeyKey, key);
    return key;
  }

  /// Simple XOR encryption for password storage
  /// Uses the device-specific key to encrypt/decrypt passwords
  static String _encryptPassword(String password) {
    final key = _getEncryptionKey();
    final keyBytes = utf8.encode(key);
    final passwordBytes = utf8.encode(password);
    
    final encryptedBytes = <int>[];
    for (int i = 0; i < passwordBytes.length; i++) {
      encryptedBytes.add(passwordBytes[i] ^ keyBytes[i % keyBytes.length]);
    }
    
    return base64.encode(encryptedBytes);
  }

  /// Decrypts the stored password using XOR decryption
  /// Returns the original password string
  static String _decryptPassword(String encryptedPassword) {
    try {
      final key = _getEncryptionKey();
      final keyBytes = utf8.encode(key);
      final encryptedBytes = base64.decode(encryptedPassword);
      
      final decryptedBytes = <int>[];
      for (int i = 0; i < encryptedBytes.length; i++) {
        decryptedBytes.add(encryptedBytes[i] ^ keyBytes[i % keyBytes.length]);
      }
      
      return utf8.decode(decryptedBytes);
    } catch (e) {
      // Return empty string if decryption fails
      return '';
    }
  }

  /// Saves user credentials when Remember Me is enabled
  /// Encrypts the password before storage for security
  /// 
  /// Parameters:
  /// - [email]: User's email address (stored in plain text)
  /// - [password]: User's password (encrypted before storage)
  /// - [rememberMe]: Whether Remember Me is enabled
  static Future<void> saveCredentials({
    required String email,
    required String password,
    required bool rememberMe,
  }) async {
    try {
      if (rememberMe) {
        // Save email in plain text
        await box.write(_savedEmailKey, email);
        
        // Encrypt and save password
        final encryptedPassword = _encryptPassword(password);
        await box.write(_savedPasswordKey, encryptedPassword);
        
        // Save Remember Me state
        await box.write(_rememberMeEnabledKey, true);
      } else {
        // Clear saved credentials if Remember Me is disabled
        await clearCredentials();
      }
    } catch (e) {
      // Log error but don't throw to prevent login disruption
      debugPrint('Error saving credentials: $e');
    }
  }

  /// Retrieves saved credentials if Remember Me is enabled
  /// Decrypts the password and returns the credentials
  /// 
  /// Returns: Map with 'email', 'password', and 'rememberMe' keys
  /// Returns empty values if no credentials are saved or Remember Me is disabled
  static Map<String, dynamic> getSavedCredentials() {
    try {
      final rememberMeEnabled = box.read(_rememberMeEnabledKey) ?? false;
      
      if (!rememberMeEnabled) {
        return {
          'email': '',
          'password': '',
          'rememberMe': false,
        };
      }

      final savedEmail = box.read(_savedEmailKey) ?? '';
      final encryptedPassword = box.read(_savedPasswordKey) ?? '';
      
      final decryptedPassword = encryptedPassword.isNotEmpty 
          ? _decryptPassword(encryptedPassword)
          : '';

      return {
        'email': savedEmail,
        'password': decryptedPassword,
        'rememberMe': rememberMeEnabled,
      };
    } catch (e) {
      // Return empty credentials if retrieval fails
      debugPrint('Error retrieving credentials: $e');
      return {
        'email': '',
        'password': '',
        'rememberMe': false,
      };
    }
  }

  /// Clears all saved credentials and Remember Me state
  /// Called when user unchecks Remember Me or during security cleanup
  static Future<void> clearCredentials() async {
    try {
      await box.remove(_savedEmailKey);
      await box.remove(_savedPasswordKey);
      await box.remove(_rememberMeEnabledKey);
    } catch (e) {
      debugPrint('Error clearing credentials: $e');
    }
  }

  /// Checks if Remember Me is currently enabled
  /// Returns true if credentials are saved and Remember Me is active
  static bool isRememberMeEnabled() {
    return box.read(_rememberMeEnabledKey) ?? false;
  }

  /// Updates only the Remember Me state without affecting stored credentials
  /// Used when user toggles the checkbox without changing credentials
  static Future<void> updateRememberMeState(bool enabled) async {
    try {
      if (enabled) {
        await box.write(_rememberMeEnabledKey, true);
      } else {
        // Clear all credentials when Remember Me is disabled
        await clearCredentials();
      }
    } catch (e) {
      debugPrint('Error updating Remember Me state: $e');
    }
  }

  /// Validates if saved credentials exist and are valid
  /// Returns true if both email and password are saved and not empty
  static bool hasSavedCredentials() {
    final credentials = getSavedCredentials();
    return credentials['rememberMe'] == true &&
           credentials['email'].toString().isNotEmpty &&
           credentials['password'].toString().isNotEmpty;
  }
}
