import 'package:get/get.dart';
import 'package:ma3ak/app/modules/update_place/controller/update_place_controller.dart';

/// Dependency injection binding for update place functionality
/// Registers controllers needed for place editing and management
class UpdatePlaceBinding extends Bindings {
  @override
  void dependencies() {
    // Register update place controller for place editing operations
    Get.lazyPut(() => UpdatePlaceController());
  }
}
