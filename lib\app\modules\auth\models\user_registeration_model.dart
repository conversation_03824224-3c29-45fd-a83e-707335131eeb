class UserRegistrationModel {
  bool? status;
  String? message;
  String? token;
  int? isOwner;

  UserRegistrationModel({this.status, this.message, this.token, this.isOwner});

  UserRegistrationModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    token = json['token'];
    isOwner = json['isOwner'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['token'] = token;
    data['isOwner'] = isOwner;
    return data;
  }
}
