import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/config/theme/app_color.dart';

/// Comment input widget with text field and send button
/// Used for adding comments with customizable hint text and callbacks
class SendComment extends StatelessWidget {
  const SendComment({
    super.key,
    required this.sendCommentcontroller,
    this.onPressed,
    this.onChanged,
    this.hintText = 'اكتب تعليقك',
  });
  
  // Widget properties and callbacks
  final TextEditingController sendCommentcontroller; // Text input controller
  final void Function()? onPressed; // Send button callback
  final void Function(String)? onChanged; // Text change callback
  final String? hintText; // Placeholder text for input field
  
  @override
  Widget build(BuildContext context) {
    return Container(
      // Fixed height for consistent comment input area
      height: 65.h,
      // Styled container with primary color and rounded top corners
      decoration: BoxDecoration(
          color: AppColor.primaryColor,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(12.r),
            topRight: Radius.circular(12.r),
          )),
      child: Padding(
        padding: const EdgeInsets.only(right: 10),
        child: Row(
          children: [
            // Expandable text input field for comment text
            Expanded(
              child: TextFormField(
                onChanged: onChanged,                    // Text change callback
                controller: sendCommentcontroller,       // Text input controller
                decoration: InputDecoration(
                  hintText: hintText,                    // Placeholder text
                  border: InputBorder.none,              // Remove default border
                  filled: true,                          // Enable background fill
                  fillColor: Colors.white,               // White background for contrast
                ),
              ),
            ),
            // Send button with icon for comment submission
            IconButton.filled(
              icon: const Icon(
                Icons.send,                              // Send arrow icon
              ),
              onPressed: onPressed,                      // Submit callback
            )
          ],
        ),
      ),
    );
  }
}
