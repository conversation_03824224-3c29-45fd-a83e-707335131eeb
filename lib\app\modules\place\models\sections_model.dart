import 'dart:convert';

// To parse this JSON data, do
//
//     final sectionsModel = sectionsModelFromJson(jsonString);

SectionsModel sectionsModelFromJson(String str) =>
    SectionsModel.fromJson(json.decode(str));

String sectionsModelToJson(SectionsModel data) => json.encode(data.toJson());

class SectionsModel {
  final bool? status;
  final String? message;
  final List<SectionDatum>? data;

  SectionsModel({
    this.status,
    this.message,
    this.data,
  });

  factory SectionsModel.fromJson(Map<String, dynamic> json) => SectionsModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : List<SectionDatum>.from(
                json["data"].map((x) => SectionDatum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? null
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class SectionDatum {
  final int? id;
  final String? name;
  final String? slug;
  final int? status;

  SectionDatum({
     this.id,
     this.name,
     this.slug,
     this.status,
  });

  factory SectionDatum.fromJson(Map<String, dynamic> json) => SectionDatum(
        id: json["id"],
        name: json["name"],
        slug: json["slug"],
        status: json["status"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "slug": slug,
        "status": status,
      };
}
