import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/theme/app_color.dart';

// Reactive button widget that shows loading state during operations
class ReactiveButton extends StatelessWidget {
  ReactiveButton({
    super.key,
    required this.wdth,
    required this.text,
    this.onPressed,
    this.color = AppColor.blueColor1,
  });

  // Button properties
  final double wdth;
  final String text;
  final void Function()? onPressed;
  final Color? color;
  final ReactiveButtonController reactiveButtonController =
      Get.put(ReactiveButtonController());
      
  // Builds reactive button with loading state management
  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: wdth * 0.7,
        // Observes loading state and switches between loading and normal button
        child: Obx(() {
          return reactiveButtonController.isLoading.value
              ? button(null, const Center(child: CircularProgressIndicator()))
              : button(
                  onPressed,
                  Text(
                    text,
                    style: const TextStyle(color: AppColor.whiteColor),
                  ),
                );
        }));
  }

  // Creates styled elevated button with custom properties
  ElevatedButton button(void Function()? onPressd, Widget? child) {
    return ElevatedButton(
        style: ButtonStyle(
            shape: const WidgetStatePropertyAll<OutlinedBorder>(
                ContinuousRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(15),
              ),
            )),
            backgroundColor: WidgetStatePropertyAll<Color>(color!)),
        onPressed: onPressd,
        child: child

        // style: ButtonStyle(w)
        );
  }
}
