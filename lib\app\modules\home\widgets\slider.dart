import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';

class SliderWidget extends StatelessWidget {
  const SliderWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final adsController = Get.find<AdsController>();

    return Container(
      height: 175.h,
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(8.r)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        // alignment: Alignment.bottomCenter,
        children: [
          Obx(() {
            return CarouselSlider.builder(
              itemCount: adsController.adData.value.data!.length,
              itemBuilder: (context, index, _) {
                final data = adsController.adData.value.data!;
                return CachedNetworkImage(
                  progressIndicatorBuilder: (context, url, progress) {
                   
                    return const Center(child: CircularProgressIndicator());
                  },
                  imageUrl: ApiList.baseUrl + data[index].img!,
                  imageBuilder: (context, imageProvider) => Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      image: DecorationImage(
                          image: imageProvider, fit: BoxFit.fill),
                    ),
                  ),
                );
              },
              options: CarouselOptions(
                autoPlayInterval: const Duration(seconds: 7),
                height: 160.h,
                viewportFraction: .9,
                autoPlay: true,
                enlargeCenterPage: true,
                disableCenter: true,
                onPageChanged: (index, reason) {
                  adsController.handleAdsDots(index);
                },
              ),
            );
          }),
          Obx(() {
            return Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                adsController.adData.value.data!.length,
                (index) => AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  margin: EdgeInsets.symmetric(horizontal: 4.w),
                  height: 8.h,
                  width: adsController.dotIndex.value == index ? 16.w : 8.w,
                  decoration: BoxDecoration(
                    color: adsController.dotIndex.value == index
                        ? AppColor.primaryColor
                        : Colors.grey,
                    borderRadius: BorderRadius.circular(4.r),
                  ),
                ),
              ),
            );
          }),
        ],
      ),
    );
  }
}
