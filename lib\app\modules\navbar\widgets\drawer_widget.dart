import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/about/views/about_screen.dart';
import 'package:ma3ak/app/modules/auth/views/login_screen.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/navbar/widgets/list_tile_drawer_widget.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:ma3ak/utils/svg_icon.dart';

/// Side navigation drawer widget with app logo and menu items
/// Provides navigation options and user account management functionality
class DrawerWidget extends StatelessWidget {
  const DrawerWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Drawer(
      // Custom rounded shape for modern drawer appearance
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          bottomLeft: Radius.circular(30),
          topLeft: Radius.circular(8),
        ),
      ),
      // Responsive width based on screen size
      width: MediaQuery.of(context).size.width * 0.75,
      child: ListView(
        padding: EdgeInsets.zero,
        children: <Widget>[
          // Drawer header section with close button and branding
          DrawerHeader(
            padding: EdgeInsets.only(right: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Close button positioned at top-right
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                        onPressed: () {
                          Get.back(); // Close drawer when tapped
                        },
                        icon: const Icon(Icons.close))
                  ],
                ),
                // Centered app logo
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(
                      width: MediaQuery.of(context).size.width * .25,
                      child: Image.asset(ImagesPic.logoM),
                    ),
                  ],
                )
              ],
            ),
          ),
          // Profile menu item - navigates to profile or login
          ListTileDrawerWidget(
            onTap: () {
              Get.find<NavbarController>().selectedIndex(0);

              if (box.read('token') != '') {
                Get.lazyPut(() => ProfileController());
              } else if (box.read('token') == '') {
                Get.to(() => const LoginScreen());
              }
              Get.back();
            },
            title: 'الملف الشخصي',
          ),
          // Places/Favorites menu item - different text for owners vs users
          ListTileDrawerWidget(
            onTap: () {
              Get.find<NavbarController>().selectedIndex(4);
              Get.back();
            },
            title: box.read('isOwner') == 1 ? 'أماكني' : 'الأماكن',
            svgIcon: SvgIcon.places,
          ),
          // Offers menu item - different text for owners vs users
          ListTileDrawerWidget(
            onTap: () {
              Get.find<NavbarController>().selectedIndex(1);
              Get.back();
            },
            title: box.read('isOwner') == 1 ? 'عروضي' : 'العروض',
            svgIcon: SvgIcon.ads,
          ),
          // About app menu item
          ListTileDrawerWidget(
            onTap: () => Get.to(() => const AboutScreen()),
            title: 'حول التطبيق',
          ),
        ],
      ),
    );
  }
}
