import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

// Reusable main app bar widget with customizable properties
class MainAppBar extends StatelessWidget implements PreferredSizeWidget {
  // App bar configuration properties
  final Widget? title;
  final Widget? action;
  final bool hideBack;
  final double? height;
  final Widget? leading;
  final bool? hideTitle;
  final bool? showAction;

  const MainAppBar(
      {this.title,
      this.hideBack = false,
      this.action,
      this.height,
      super.key,
      this.leading,
      this.hideTitle = false,
      this.showAction = false});

  // Builds the app bar with logo and optional menu button
  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: AppColor.primaryBack,
      elevation: 0,
      centerTitle: true,
      automaticallyImplyLeading: false,
      surfaceTintColor: Colors.transparent,
      // toolbarHeight: height ?? 80,
      title: title,
      // Right side actions - either custom action or app logo
      actions: [
        showAction == true
            ? action ?? Container()
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: SizedBox(
                  height: 65.h,
                  width: 65.w,
                  child: Image.asset(ImagesPic.logoM),
                ),
              )
      ],
      // Left side leading - menu button or custom widget
      leading: hideBack
          ? null
          : IconButton(
              onPressed: () async {
                Scaffold.of(context).openDrawer();
              },
              icon: const Icon(
                Icons.menu,
              ),
            ),
    );
  }

  // Defines the preferred height of the app bar
  @override
  Size get preferredSize => Size.fromHeight(height ?? 80);
}
