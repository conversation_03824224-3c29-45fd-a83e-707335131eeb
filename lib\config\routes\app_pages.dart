import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/views/login_screen.dart';
import 'package:ma3ak/app/modules/auth/views/register_screen.dart';
import 'package:ma3ak/app/modules/home/<USER>/home_bindings.dart';
import 'package:ma3ak/app/modules/navbar/bindings/navbar_binding.dart';
import 'package:ma3ak/app/modules/navbar/views/nav_bar_view.dart';
import 'package:ma3ak/app/modules/splash/binding/splash_binding.dart';
import 'package:ma3ak/app/modules/splash/views/splash_screen.dart';
import 'package:ma3ak/app/modules/update_place/views/update_place_screen.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/core/middleware/auth_middleware.dart';
import 'package:ma3ak/core/middleware/role_middleware.dart';

/// App pages configuration with GetX routing and bindings
/// Defines all application routes, pages, and their dependency bindings
class AppPages {
  AppPages._();

  // Initial route when app starts - splash screen entry point
  static String initial = "/splash/";

  // Complete list of app pages with routes, widgets, and dependency bindings
  static final pages = [
    // Splash screen - app entry point
    GetPage(
      name: initial,
      page: () => const SplashScreen(),
      binding: SplashBinding(),
    ),
    
    // Main navigation with bottom navbar
    GetPage(
      name: Routes.navBarView,
      page: () =>  const NavBarView(),
      bindings: [
        HomeBindings(),
        NavbarBinding(),
      ],
      middlewares: [
        AuthMiddleware(),
      ],
    ),
    
    // Place update screen for owners
    GetPage(
      name: Routes.updatePlace,
      page: () =>  UpdatePlaceScreen(),
      bindings: [
        HomeBindings(),
      ],
      middlewares: [
        AuthMiddleware(),
        RoleMiddleware(),
      ],
    ),

    // Authentication screens
    GetPage(
      name: Routes.login,
      page: () => const LoginScreen(),
    ),
    GetPage(
      name: Routes.register,
      page: () => const RegisterScreen(),
    ),
    
    // GetPage(
    //   name: Routes.home,
    //   page: () => const HomeScreen(),
    //   binding: HomeBindings(),
    // ),
    // GetPage(
    //   name: Routes.language,
    //   page: () => const ChangeLanguageView(),
    //   binding: LanguageBindings(),
    // )
  ];
}
