import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/controller/send_commen_controller.dart';
import 'package:ma3ak/app/modules/offers/models/place_information_old_model.dart';
import 'package:ma3ak/app/modules/place/controller/comments_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/widgets/comments_place_test_pass_all.dart';
import 'package:ma3ak/app/modules/place/widgets/current_offers_in_place_pass_all.dart';
import 'package:ma3ak/app/modules/place/widgets/description_details_widget_test.dart';
import 'package:ma3ak/app/modules/place/widgets/name_location_rating_widget_test.dart';
import 'package:ma3ak/app/modules/place/widgets/open_whats_app_number.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

/// Detailed place screen displaying place information, offers, and comments
/// Provides comprehensive view of a place with interactive features
class PlaceScreen extends StatelessWidget {
  PlaceScreen({
    super.key,
    required this.placeInfromation,
  });

  // Controller for managing comment submission functionality
  final SendCommentController sendCommentController =
      Get.put(SendCommentController());

  // Observable place information data
  final Rx<PlaceInfromationOldModel> placeInfromation;

  // Controller for managing place-related operations
  final PlaceController placeControllr = Get.put(PlaceController());

  /// Builds the place screen with loading state and content sections
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Show loading indicator while data is being fetched
      if (placeControllr.isLoading.value) {
        return const Center(
          child: CircularProgressIndicator(),
        );
      }

      // Extract place information from observable data
      var placeInfo = placeInfromation.value.data;

      return Scaffold(
        // Fixed bottom sheet for comment input
        bottomSheet: Obx(
          () => Container(
            height: 65.h,
            decoration: BoxDecoration(
                color: AppColor.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(12.r),
                  topRight: Radius.circular(12.r),
                )),
            child: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Row(
                children: [
                  // Comment text input field
                  Expanded(
                    child: TextFormField(
                      onChanged: (value) {
                        sendCommentController.sendText.value = value;
                      },
                      controller: sendCommentController.sendTextController,
                      decoration: const InputDecoration(
                        hintText: 'اكتب تعليقك',
                        border: InputBorder.none,
                        filled: true,
                        fillColor: Colors.white,
                      ),
                    ),
                  ),
                  // Send comment button
                  IconButton.filled(
                    icon: const Icon(Icons.send),
                    onPressed: sendCommentController.sendText.value == ''
                        ? null
                        : () async {
                            // Check if user is logged in before allowing comment submission
                            if (box.read('token') == null ||
                                box.read('token') == '') {
                              Get.snackbar('الرسالة', 'يجب تسجيل الدخول اولا',
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                  icon: const Icon(Icons.warning,
                                      color: Colors.white));
                            } else {
                              // Submit comment and refresh comments list
                              await sendCommentController.sendComment(
                                  slugPlace: placeInfo?.slug ?? '');
                              await Get.find<CommentsController>()
                                  .fetchCommentsPlace(placeInfo?.slug ?? '');
                            }
                          },
                  )
                ],
              ),
            ),
          ),
        ),
        // Main body with nested scroll view for collapsible header
        body: NestedScrollView(
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              // Collapsible app bar with place cover image
              SliverAppBar(
                backgroundColor: AppColor.primaryColor,
                expandedHeight: 300.h,
                pinned: true,
                flexibleSpace: FlexibleSpaceBar(
                  // Place cover image as background
                  background: CachedNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: ApiList.baseUrl + placeInfo.coverImage!,
                    imageBuilder: (context, imageProvider) => Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        color: Colors.white,
                        image: DecorationImage(
                          fit: BoxFit.contain,
                          image: imageProvider,
                        ),
                      ),
                    ),
                  ),
                  centerTitle: true,
                ),
              ),
            ];
          },
          // Scrollable content body with pull-to-refresh
          body: RefreshIndicator(
            onRefresh: () async {
              // Refresh comments when user pulls down
              await Get.find<CommentsController>()
                  .fetchCommentsPlace(placeInfo.slug!);
            },
            child: ListView(
              children: [
                // Place name, location, and rating widget
                NameLocationRatingWidgetTest(
                  slugPlace: placeInfo!.slug!,
                  namePlace: placeInfo.name!,
                  governor: placeInfo.governor!,
                  directorate: placeInfo.directorate!,
                  addressDetails: placeInfo.addressDetails!,
                  ratingPlace: placeInfo.stars!.toDouble(),
                ),
                // Place description widget
                DescriptionDetailsWidgetTest(des: placeInfo.des!),
                // WhatsApp contact button
                OpenWhatsAppNumber(
                    phoneNumber: placeInfo.whatsappNumber.toString()),
                // Current offers section
                CurrentOffersInPlacePassAll(
                  slugPlace: placeInfo.slug ?? '',
                  namePlace: placeInfo.name ?? '',
                ),
                // Comments section
                CommentsPlaceTestPassAll(placeComments: placeInfo.comments),
                // Bottom spacing for comment input
                const SizedBox(height: 70)
              ],
            ),
          ),
        ),
      );
    });
  }
}
