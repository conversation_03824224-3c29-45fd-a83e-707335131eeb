import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Controller for managing bottom navigation bar and page selection
/// Handles navigation state, scroll behavior, and drawer interactions
class NavbarController extends GetxController {
  // Currently selected tab index (default: 2 - home tab)
  final selectedIndex = 2.obs;

  // Scroll controller for main content area with drawer trigger functionality
  final ScrollController scrollController = ScrollController();

  // Controls whether the screen can be popped (back navigation)
  RxBool canPop = false.obs;

  /// Initialize controller and set up scroll listener for drawer interaction
  @override
  void onInit() {
    super.onInit();
    scrollController.addListener(_onScroll);
  }

  /// Updates selected tab index when user taps navigation item
  /// Triggers UI update to show selected tab content
  void selectPage(int index) {
    selectedIndex.value = index;
  }

  /// Handles scroll events to trigger drawer opening
  /// Opens drawer when user scrolls down more than 100 pixels
  void _onScroll() {
    if (scrollController.position.pixels > 100) {
      Get.context?.findAncestorStateOfType<ScaffoldState>()?.openDrawer();
    }
  }

  /// Clean up resources when controller is disposed
  /// Removes listeners and disposes scroll controller to prevent memory leaks
  @override
  void onClose() {
    scrollController.removeListener(_onScroll);
    scrollController.dispose();
    super.onClose();
  }
}
