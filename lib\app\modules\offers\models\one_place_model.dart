import 'dart:convert';

// To parse this JSON data, do
//
//     final onePlaceModell = onePlaceModellFromJson(jsonString);

OnePlaceModel onePlaceModellFromJson(String str) => OnePlaceModel.fromJson(json.decode(str));

String onePlaceModellToJson(OnePlaceModel data) => json.encode(data.toJson());

class OnePlaceModel {
  final bool? status;
  final String? message;
  final PlaceDatum? data; // Changed from List<PlaceDatum>? to PlaceDatum?

  OnePlaceModel({
    this.status,
    this.message,
    this.data,
  });

  factory OnePlaceModel.fromJson(Map<String, dynamic> json) => OnePlaceModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null ? null : PlaceDatum.fromJson(json["data"]), // Updated to handle single object
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data?.toJson(), // Updated to serialize single object
      };
}

class PlaceDatum {
  final int id;
  final String name;
  final String governor;
  final String directorate;
  final String addressDetails;
  final String des;
  final int whatsappNumber;
  final String coverImage;
  final String slug;
  final int stars;
  final String status;
  final int? parentId;
  final int catId;
  final int ownerId;

  PlaceDatum({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
  });

  factory PlaceDatum.fromJson(Map<String, dynamic> json) => PlaceDatum(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
      };
}
