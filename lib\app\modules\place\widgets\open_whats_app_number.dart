import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/config/theme/app_color.dart';

/// Widget for displaying phone number with copy and WhatsApp actions
class OpenWhatsAppNumber extends StatelessWidget {
  const OpenWhatsAppNumber({
    super.key,
    required this.phoneNumber,
  });

  // Phone number to display and use for actions
  final String phoneNumber;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        children: [
          // Copy phone number button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.copy),
              style: const ButtonStyle(
                  shape: WidgetStatePropertyAll<OutlinedBorder>(
                      ContinuousRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(15),
                    ),
                  )),
                  backgroundColor: WidgetStatePropertyAll<Color>(Colors.green)),
              onPressed: () {
                // Copy phone number to clipboard
                Get.find<PlaceController>().copyPhoneNumber(phoneNumber);
              },
              label:  Text(
              phoneNumber,
                style: const TextStyle(color: AppColor.whiteColor),
              ),
            ),
          ),
        
          // Open WhatsApp button  
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              icon: const Icon(Icons.wechat),
              style: const ButtonStyle(
                  shape: WidgetStatePropertyAll<OutlinedBorder>(
                      ContinuousRectangleBorder(
                    borderRadius: BorderRadius.all(
                      Radius.circular(15),
                    ),
                  )),
                  backgroundColor: WidgetStatePropertyAll<Color>(Colors.green)),
              onPressed: () {
                // Open WhatsApp with the phone number
                Get.find<PlaceController>().openWhatsApp(phoneNumber);
              },
              label: const Text(
                'فتح في الوتس اب',
                style: TextStyle(color: AppColor.whiteColor),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
