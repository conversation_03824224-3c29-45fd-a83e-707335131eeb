import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/my_offers_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/update_offer_controller.dart';
import 'package:ma3ak/app/modules/offers/widgets/text_form_field_offer_widget.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/widgets/basic_button.dart';

class UpdateOfferScreen extends StatelessWidget {
  UpdateOfferScreen({super.key});
  final updateOfferController = Get.find<UpdateOfferController>();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(),
      body: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Column(
              children: [
                TextFormFieldOfferWidget(
                  controller: updateOfferController.titleController,
                  labelText: 'اسم العرض',
                  hintText: 'ادخل اسم العرض',
                ),
                TextFormFieldOfferWidget(
                  controller: updateOfferController.desController,
                  labelText: 'اسم الوصف',
                  hintText: 'ادخل اسم الوصف',
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16.0),
                  child: Obx(
                    () => DropdownButtonFormField<String>(
                      value: updateOfferController.selectedType.value.isEmpty
                          ? null
                          : updateOfferController.selectedType.value,
                      decoration: InputDecoration(
                        contentPadding:
                            const EdgeInsets.symmetric(horizontal: 10),
                        labelText: 'اسم النوع',
                        hintText: 'اختر نوع العرض',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                      ),
                      onChanged: (String? newValue) {
                        updateOfferController.selectedType.value = newValue!;
                      },
                      items: updateOfferController.types
                          .map((type) => DropdownMenuItem<String>(
                                value: type,
                                child: Text(type),
                              ))
                          .toList(),
                    ),
                  ),
                ),
                const SizedBox(
                  height: 10,
                ),
                Obx(
                  () => SizedBox(
                      child: updateOfferController
                                  .selectedImagePath.value.isEmpty &&
                              updateOfferController.image.value.isEmpty
                          ? const Padding(
                              padding: EdgeInsets.all(16.0),
                              child: Text('لا توجد صورة ,الرجاء اضافة صورة'),
                            )
                          : Obx(
                              () => Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        .25,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.withOpacity(.5),
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                          image: updateOfferController
                                                  .selectedImagePath
                                                  .value
                                                  .isEmpty
                                              ? NetworkImage(ApiList.baseUrl +
                                                  updateOfferController
                                                      .image.value)
                                              : FileImage(File(
                                                  updateOfferController
                                                      .selectedImagePath
                                                      .value)),
                                          fit: BoxFit.contain),
                                    )),
                              ),
                            )),
                )
              ],
            ),
            Column(
              children: [
                IconButton.outlined(
                  onPressed: () async {
                    await updateOfferController.pickOneImage();
                  },
                  icon: const Icon(
                    size: 40,
                    Icons.add_photo_alternate,
                  ),
                ),
                const SizedBox(height: 10),
                Builder(builder: (context) {
                  return Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: BasicButton(
                        wdth: double.infinity,
                        text: 'حفظ',
                        onPressed: () async {
                          await updateOfferController.updateOffer(
                              slugOffer: updateOfferController.slugOffer.value);
                          await Get.find<MyOffersController>()
                              .fetchMyOffersOffers();
                          Get.back();
                        }),
                  );
                }),
                const SizedBox(
                  height: 40,
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
