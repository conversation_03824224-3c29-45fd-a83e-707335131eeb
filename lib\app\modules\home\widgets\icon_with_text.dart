import 'package:flutter/material.dart';

/// Reusable widget that displays an icon with accompanying text
/// Used for creating consistent icon-text pairs throughout the app
class IconWithText extends StatelessWidget {
  const IconWithText({
    super.key,
    required this.icon,
    required this.title,
  });
  
  // Icon to display alongside the text
  final IconData icon;
  // Text content to show next to the icon
  final String title;

  @override
  Widget build(BuildContext context) {
    return Padding(
      // Minimal vertical padding for consistent spacing
      padding: const EdgeInsets.symmetric(vertical: 0),
      child: Row(
        // 7px spacing between icon and text for visual balance
        spacing: 7,
        children: [
          // Small icon with fixed size for consistency
          Icon(
            icon,
            size: 14,
          ),
          // Compact text with small font size
          Text(
            title,
            style: const TextStyle(fontSize: 10),
          )
        ],
      ),
    );
  }
}
