import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/utils/images_pic.dart';

import '../../../../config/theme/app_color.dart';

// Splash screen displayed when app launches
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  // Builds splash screen with logo and auto-navigation
  @override
  Widget build(BuildContext context) {
    // Auto-navigate to main screen after 3 seconds
    Timer(const Duration(seconds: 3), () {
      Get.offNamed(Routes.navBarView);
    });
    
    // Configure system UI overlay style
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value:const SystemUiOverlayStyle(
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
        statusBarIconBrightness: Brightness.dark,
        statusBarColor: Colors.transparent,
        statusBarBrightness: Brightness.dark,
      ),
      
      // Main splash screen UI
      child: Scaffold(
        backgroundColor: AppColor.primaryBackgroundColor,
        body: Center(
          // Display app logo in center
          child: Image.asset(
         ImagesPic.logoM,
          height: 200.h,
        )
        ),
      ),
    );
  }
}
