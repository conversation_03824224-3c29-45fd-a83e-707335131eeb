import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';
import 'package:ma3ak/main.dart';

class LogoutAccountController extends GetxController {
  var isLoading = false.obs;

  Future<void> logoutUser() async {
    isLoading(true);
    // Call the logoutUser method from the authentication service
    var response = await RemoteServicesImpl().logoutUser();
    isLoading(false);
    response.fold((l) => Get.snackbar("Error", l), (r) async {
      // Clear user session data
      await _clearUserSession();

      // Navigate to login screen and clear navigation stack
      Get.offAllNamed(Routes.login);

      Get.snackbar(
        "Success",
        r.message.toString(),
      );
      Get.find<ReactiveButtonController>().isLoading.value = false;
    });
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }

  Future<void> logoutOwner() async {
    isLoading(true);
    var response = await RemoteServicesImpl().logoutOwner();
    isLoading(false);
    response.fold((l) => Get.snackbar("Error", l), (r) async{
      // Clear user session data
      await _clearUserSession();

      // Navigate to login screen and clear navigation stack
      Get.offAllNamed(Routes.login);

      Get.snackbar(
        "Success",
        r.message.toString(),
      );
      Get.find<ReactiveButtonController>().isLoading.value = false;
    });
    Get.find<ReactiveButtonController>().isLoading.value = false;
  }

  /// Clears user session data from local storage
  /// Note: Remember Me credentials are intentionally preserved during logout
  /// They are only cleared when user explicitly unchecks Remember Me option
  Future<void> _clearUserSession() async {
    try {
      // Clear authentication token
      await box.remove('token');

      // Clear user type flag
      await box.remove('isOwner');

      // Note: Remember Me credentials (saved_email, saved_password, remember_me_enabled)
      // are intentionally NOT cleared here to persist across logout operations
      // They are only cleared when user unchecks Remember Me in LoginController

      // Clear any other user-related data if needed
      // Add more session data clearing here as required
    } catch (e) {
      // Log error but don't prevent logout
      debugPrint('Error clearing session data: $e');
    }
  }
}
