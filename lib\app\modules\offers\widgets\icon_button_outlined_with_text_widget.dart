import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/app/modules/offers/widgets/icon_button_outlined_widget.dart';

class IconButtonOutlinedWithTextWidget extends StatelessWidget {
  const IconButtonOutlinedWithTextWidget({
    super.key,
    this.icon = Icons.handshake,
    this.number = '0',
    this.onPressed,
  });
  final IconData? icon;
  final String? number;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Row(children: [
      IconButtonOutlinedWidget(
        onPressed: onPressed,
        icon: icon,
      ),
      SizedBox(
        width: 5.w,
      ),
      Text(
        number!,
        style: TextStyle(fontSize: 16.sp),
      )
    ]);
  }
}
