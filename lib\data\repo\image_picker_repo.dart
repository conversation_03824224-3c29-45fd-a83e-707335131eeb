import 'dart:io';
import 'package:image_picker/image_picker.dart';

/// Repository class for handling image picking operations
/// Provides methods to select images from device gallery
class ImagePickerRepo {
  // Image picker instance for gallery operations
  final ImagePicker _picker = ImagePicker();

  /// Picks an image from the device gallery
  /// Returns File object if image is selected, null otherwise
  Future<File?> pickImage() async {
    final pickedFile = await _picker.pickImage(source: ImageSource.gallery);
    return pickedFile != null ? File(pickedFile.path) : null;
  }
}
