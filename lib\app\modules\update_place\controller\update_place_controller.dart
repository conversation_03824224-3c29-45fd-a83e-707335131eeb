import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ma3ak/app/modules/update_place/models/update_place_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

// Controller for managing place update functionality and form data
class UpdatePlaceController extends GetxController {
  // Loading state for UI feedback
  var isloading = false.obs;
  // Reactive model to store update response data
  Rx<UpdatePlaceModel> updatePlaceData = UpdatePlaceModel().obs;

  // Form controllers for place information input fields
  TextEditingController nameController = TextEditingController();
  TextEditingController governorController = TextEditingController();
  TextEditingController directorateController = TextEditingController();
  TextEditingController addressDetailsController = TextEditingController();
  TextEditingController desController = TextEditingController();
  TextEditingController whatsappNumberController = TextEditingController();
  
  // Selected image path for cover image upload
  var selectedImagePath = ''.obs;
// cover_image
// address_details
// address_details
  // Image picker instance for selecting cover images
  final ImagePicker _imagePicker = ImagePicker();

  // Opens gallery to select a single image for place cover
  Future<void> pickOneImage() async {
    try {
      final pickedImage =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        selectedImagePath.value = pickedImage.path;
      } else {
        Get.snackbar('Error', 'No Image Selected');
      }
    } catch (e) {
      Get.snackbar('Error', e.toString());
    }
  }

  // Sends place update request with form data and optional image
  Future<void> updatePlace(String slugPlace) async {
    final multipartFile = await convertFileToMultipartFile();
    
    // Prepare form data with place information
    final formData = FormData({
      // 'name': nameController.text,
      'governor': governorController.text,
      'directorate': directorateController.text,
      'address_details': addressDetailsController.text,
      'des': desController.text,
      'whatsapp_number': whatsappNumberController.text,
    });
    
    // Add cover image if selected
    if (multipartFile == null) {
      // Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(MapEntry('cover_image', multipartFile));
    }
    
    // Make API call to update place
    try {
      final response = await RemoteServicesImpl()
          .updatePlace(body: formData, slugPlace: slugPlace);
      
      // Handle API response
      response.fold(
        (error) {
          Get.snackbar('Error', error.toString());
        },
        (dataModel) {
          updatePlaceData.value = dataModel;
          // Get.snackbar('Success', 'تم التعديل بنجاح');
          if (dataModel.status == true) {
            Get.snackbar(' نجاح', 'تم التعديل بنجاح');
          } else if (dataModel.status == false) {
            Get.snackbar('فشل', dataModel.message.toString());
          }
        },
      );
    } catch (e) {
      // Get.snackbar('Error', e.toString());
    }
  }

  // Converts selected image file to MultipartFile for API upload
  Future<MultipartFile?> convertFileToMultipartFile() async {
    if (selectedImagePath.isNotEmpty) {
      return MultipartFile(selectedImagePath.value,
          filename: selectedImagePath.value.split('/').last,
          contentType: "multipart/form-data");
    }
    return null;
  }
}
