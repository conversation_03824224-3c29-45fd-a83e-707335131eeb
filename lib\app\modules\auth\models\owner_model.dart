class OwnerModel {
  Owner? owner;
  String? token;
  int? isOwner;

  OwnerModel({this.owner, this.token, this.isOwner});

  OwnerModel.fromJson(Map<String, dynamic> json) {
    owner = json['owner'] != null ? Owner.fromJson(json['owner']) : null;
    token = json['token'];
    isOwner = json['isOwner'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    if (owner != null) {
      data['owner'] = owner!.toJson();
    }
    data['token'] = token;
    data['isOwner'] = isOwner;
    return data;
  }
}

class Owner {
  int? id;
  String? name;
  String? email;
  String? phoneNumber;
  int? isOwner;
  String? slug;
  String? img;
  Null emailVerifiedAt;
  String? status;
  String? createdAt;
  String? updatedAt;

  Owner(
      {this.id,
      this.name,
      this.email,
      this.phoneNumber,
      this.isOwner,
      this.slug,
      this.img,
      this.emailVerifiedAt,
      this.status,
      this.createdAt,
      this.updatedAt});

  Owner.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    email = json['email'];
    phoneNumber = json['phone_number'];
    isOwner = json['isOwner'];
    slug = json['slug'];
    img = json['img'];
    emailVerifiedAt = json['email_verified_at'];
    status = json['status'];
    createdAt = json['created_at'];
    updatedAt = json['updated_at'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['name'] = name;
    data['email'] = email;
    data['phone_number'] = phoneNumber;
    data['isOwner'] = isOwner;
    data['slug'] = slug;
    data['img'] = img;
    data['email_verified_at'] = emailVerifiedAt;
    data['status'] = status;
    data['created_at'] = createdAt;
    data['updated_at'] = updatedAt;
    return data;
  }
}
