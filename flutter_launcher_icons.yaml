# flutter pub run flutter_launcher_icons
flutter_launcher_icons:
  image_path: "assets/logo/new_logo_with_text.png"

  # Android configuration
  android: "ic_launcher"
  min_sdk_android: 21 # android min sdk min:16, default 21
  adaptive_icon_background: "#ffffff"
  adaptive_icon_foreground: "assets/logo/new_logo_with_text.png"
  adaptive_icon_monochrome: "assets/logo/new_logo_with_text.png"
  adaptive_icon_foreground_inset: 25

  # iOS configuration
  ios: true
  remove_alpha_ios: true

  # Web configuration
  web:
    generate: true
    image_path: "assets/logo/new_logo_with_text.png"
    background_color: "#ffffff"
    theme_color: "#ffffff"

  # Windows configuration
  windows:
    generate: true
    image_path: "assets/logo/new_logo_with_text.png"
    icon_size: 48 # min:48, max:256, default: 48

  # macOS configuration
  macos:
    generate: true
    image_path: "assets/logo/new_logo_with_text.png"

