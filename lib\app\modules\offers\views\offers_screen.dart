import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';

import 'package:ma3ak/app/modules/offers/widgets/card_offer.dart';
import 'package:ma3ak/app/modules/offers/widgets/filter_type_widget.dart';
import 'package:ma3ak/app/modules/offers/widgets/search_text_field_offers.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';

// Screen displaying all available offers with search and filter functionality
class OffersScreen extends StatelessWidget {
  OffersScreen({
    super.key,
  });

  final OffersController offersController = Get.find<OffersController>();
  // Builds the offers screen with search, filter, and list functionality
  @override
  Widget build(BuildContext context) {
    return RefreshIndicator(
      // Pull-to-refresh functionality
      onRefresh: () async {
        await offersController.fetchOffers();
      },
      child: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            const MainAppBar(),
            // Search field for filtering offers
            SearchTextFieldOffers(
              searchController: offersController.searchController,
              onChanged: (text) {
                offersController.searchOffers(text);
              },
            ),
            // Filter options widget
            FilterTypeWidget(),
            const SizedBox(
              height: 10,
            ),
            // Reactive offers list with empty state handling
            Obx(() {
              if (offersController.searchResult!.isEmpty) {
                // Empty state when no offers found
                return const Padding(
                  padding: EdgeInsets.all(8.0),
                  child: Center(
                      child: Text(
                    'لا توجد عروض',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  )),
                );
              } else {
                // Offers list view
                return Expanded(
                  child: ListView.separated(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      var offer = offersController.searchResult?[index];
                      return InkWell(
                          // Handle offer tap with loading dialog
                          onTap: () async {
                            Get.defaultDialog(
                              barrierDismissible:
                                  false, // Prevent dismissing the dialog
                              title: 'جاري التحميل...',
                              content: const Center(
                                  child: CircularProgressIndicator()),
                            );
                            var res = await offersController.getPlaceByOffer(
                              slugOffer: offer.slug,
                            );
                            res.fold((error) {
                              // Dismiss the loading dialog after data is fetched
                              Get.back();
                              debugPrint(error.toString());
                            }, (dataModel) async {
                              offersController.placeInfromationOld.value =
                                  dataModel;
                              await Get.find<OffersController>()
                                  .fetchOffersByPlace(
                                      slugPlace: offersController
                                          .placeInfromationOld
                                          .value
                                          .data!
                                          .slug!);
                              // Dismiss the loading dialog after data is fetched
                              Get.back();
                              if (box.read('isOwner') == 1) {
                                Get.to(() => PlaceScreenOwner(
                                      placeInfromation:
                                          offersController.placeInfromationOld,
                                    ));
                              } else {
                                Get.to(() => PlaceScreen(
                                    placeInfromation:
                                        offersController.placeInfromationOld));
                              }
                            });
                          },
                          child: CardOffer(offer: offer!));
                    },
                    separatorBuilder: (context, index) {
                      return const SizedBox(
                        height: 10,
                      );
                    },
                    itemCount: offersController.searchResult?.length ?? 0,
                  ),
                );
              }
            })
          ],
        ),
      ),
    );
  }
}
