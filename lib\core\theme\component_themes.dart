import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/config/theme/app_color.dart';

/// Component-specific theme configurations
/// Provides consistent styling for all UI components across the application
class ComponentThemes {
  ComponentThemes._();

  /// App bar theme configuration
  static AppBarTheme get appBarTheme {
    return AppBarTheme(
      backgroundColor: Colors.transparent,
      elevation: 0,
      centerTitle: true,
      iconTheme: const IconThemeData(
        color: AppColor.blackColor,
        size: 24,
      ),
      titleTextStyle: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: AppColor.titleTextColor,
      ),
      toolbarHeight: 80.h,
    );
  }

  /// Bottom navigation bar theme
  static BottomNavigationBarThemeData get bottomNavigationBarTheme {
    return BottomNavigationBarThemeData(
      backgroundColor: AppColor.whiteColor,
      selectedItemColor: AppColor.primaryColor,
      unselectedItemColor: AppColor.deSelectedColor,
      type: BottomNavigationBarType.fixed,
      elevation: 5,
      selectedLabelStyle: TextStyle(
        fontSize: 12.sp,
        fontWeight: FontWeight.w500,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 10.sp,
        fontWeight: FontWeight.normal,
      ),
    );
  }

  /// Elevated button theme
  static ElevatedButtonThemeData get elevatedButtonTheme {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColor.primaryColor,
        foregroundColor: AppColor.whiteColor,
        elevation: 2,
        shadowColor: AppColor.primaryColor.withValues(alpha: 0.3),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 24.w,
          vertical: 12.h,
        ),
        textStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Text button theme
  static TextButtonThemeData get textButtonTheme {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        foregroundColor: AppColor.primaryColor,
        textStyle: TextStyle(
          fontSize: 14.sp,
          fontWeight: FontWeight.w500,
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 16.w,
          vertical: 8.h,
        ),
      ),
    );
  }

  /// Outlined button theme
  static OutlinedButtonThemeData get outlinedButtonTheme {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        foregroundColor: AppColor.primaryColor,
        side: const BorderSide(
          color: AppColor.primaryColor,
          width: 1.5,
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12.r),
        ),
        padding: EdgeInsets.symmetric(
          horizontal: 24.w,
          vertical: 12.h,
        ),
        textStyle: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Input decoration theme
  static InputDecorationTheme get inputDecorationTheme {
    return InputDecorationTheme(
      filled: true,
      fillColor: AppColor.whiteColor,
      contentPadding: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 12.h,
      ),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(
          color: AppColor.blueBorderColor,
          width: 1,
        ),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(
          color: AppColor.blueBorderColor,
          width: 1,
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(
          color: AppColor.primaryColor,
          width: 2,
        ),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(
          color: AppColor.error,
          width: 1,
        ),
      ),
      focusedErrorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(12.r),
        borderSide: const BorderSide(
          color: AppColor.error,
          width: 2,
        ),
      ),
      labelStyle: TextStyle(
        fontSize: 14.sp,
        color: AppColor.textColor1,
      ),
      hintStyle: TextStyle(
        fontSize: 14.sp,
        color: AppColor.textColor1,
      ),
      errorStyle: TextStyle(
        fontSize: 12.sp,
        color: AppColor.error,
      ),
    );
  }

  /// Card theme
  static CardTheme get cardTheme {
    return CardTheme(
      color: AppColor.whiteColor,
      elevation: 2,
      shadowColor: AppColor.blackColor.withValues(alpha: 0.1),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16.r),
      ),
      margin: EdgeInsets.symmetric(
        horizontal: 16.w,
        vertical: 8.h,
      ),
    );
  }

  /// Dialog theme
  static DialogTheme get dialogTheme {
    return DialogTheme(
      backgroundColor: AppColor.whiteColor,
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.r),
      ),
      titleTextStyle: TextStyle(
        fontSize: 18.sp,
        fontWeight: FontWeight.w600,
        color: AppColor.titleTextColor,
      ),
      contentTextStyle: TextStyle(
        fontSize: 14.sp,
        color: AppColor.textColor,
      ),
    );
  }

  /// Snackbar theme
  static SnackBarThemeData get snackBarTheme {
    return SnackBarThemeData(
      backgroundColor: AppColor.blackColor1,
      contentTextStyle: TextStyle(
        fontSize: 14.sp,
        color: AppColor.whiteColor,
      ),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.r),
      ),
      behavior: SnackBarBehavior.floating,
    );
  }

  /// Floating action button theme
  static FloatingActionButtonThemeData get floatingActionButtonTheme {
    return FloatingActionButtonThemeData(
      backgroundColor: AppColor.primaryColor,
      foregroundColor: AppColor.whiteColor,
      elevation: 6,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(56.r),
      ),
    );
  }

  /// Bottom app bar theme
  static BottomAppBarTheme get bottomAppBarTheme {
    return const BottomAppBarTheme(
      color: AppColor.whiteColor,
      elevation: 5,
      shape: CircularNotchedRectangle(),
    );
  }

  /// Divider theme
  static DividerThemeData get dividerTheme {
    return DividerThemeData(
      color: AppColor.borderColor,
      thickness: 1,
      space: 1,
      indent: 16.w,
      endIndent: 16.w,
    );
  }

  /// Icon theme
  static IconThemeData get iconTheme {
    return const IconThemeData(
      color: AppColor.textColor,
      size: 24,
    );
  }

  /// Primary icon theme
  static IconThemeData get primaryIconTheme {
    return const IconThemeData(
      color: AppColor.whiteColor,
      size: 24,
    );
  }
}
