/// Validation utilities for form inputs
/// Provides comprehensive validation patterns and error messages
class ValidationUtils {
  ValidationUtils._();

  // Email validation regex pattern
  static final RegExp _emailRegex = RegExp(
    r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
  );

  // Password validation regex patterns
  static final RegExp _passwordUpperCase = RegExp(r'[A-Z]');
  static final RegExp _passwordLowerCase = RegExp(r'[a-z]');
  static final RegExp _passwordDigit = RegExp(r'[0-9]');
  static final RegExp _passwordSpecialChar = RegExp(r'[!@#$%^&*(),.?":{}|<>]');

  // Phone number validation regex (supports various formats)
  static final RegExp _phoneRegex = RegExp(
    r'^[\+]?[1-9][\d]{0,15}$',
  );

  /// Validates email address format
  /// Returns null if valid, error message if invalid
  static String? validateEmail(String? email) {
    if (email == null || email.isEmpty) {
      return 'البريد الإلكتروني مطلوب'; // Email is required
    }

    if (!_emailRegex.hasMatch(email.trim())) {
      return 'يرجى إدخال بريد إلكتروني صحيح'; // Please enter a valid email
    }

    return null;
  }

  /// Validates password strength
  /// Returns null if valid, error message if invalid
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'كلمة المرور مطلوبة'; // Password is required
    }

    if (password.length < 8) {
      return 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'; // Password must be at least 8 characters
    }

    if (!_passwordUpperCase.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل'; // Password must contain at least one uppercase letter
    }

    if (!_passwordLowerCase.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل'; // Password must contain at least one lowercase letter
    }

    if (!_passwordDigit.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل'; // Password must contain at least one digit
    }

    if (!_passwordSpecialChar.hasMatch(password)) {
      return 'كلمة المرور يجب أن تحتوي على رمز خاص واحد على الأقل'; // Password must contain at least one special character
    }

    return null;
  }

  /// Validates password confirmation
  /// Returns null if valid, error message if invalid
  static String? validatePasswordConfirmation(String? password, String? confirmPassword) {
    if (confirmPassword == null || confirmPassword.isEmpty) {
      return 'تأكيد كلمة المرور مطلوب'; // Password confirmation is required
    }

    if (password != confirmPassword) {
      return 'كلمات المرور غير متطابقة'; // Passwords do not match
    }

    return null;
  }

  /// Validates phone number format
  /// Returns null if valid, error message if invalid
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) {
      return 'رقم الهاتف مطلوب'; // Phone number is required
    }

    // Remove spaces and special characters for validation
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    if (!_phoneRegex.hasMatch(cleanPhone)) {
      return 'يرجى إدخال رقم هاتف صحيح'; // Please enter a valid phone number
    }

    if (cleanPhone.length < 10 || cleanPhone.length > 15) {
      return 'رقم الهاتف يجب أن يكون بين 10 و 15 رقم'; // Phone number must be between 10 and 15 digits
    }

    return null;
  }

  /// Validates required text field
  /// Returns null if valid, error message if invalid
  static String? validateRequired(String? value, String fieldName) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName مطلوب'; // Field is required
    }
    return null;
  }

  /// Validates name field (letters and spaces only)
  /// Returns null if valid, error message if invalid
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'الاسم مطلوب'; // Name is required
    }

    if (name.trim().length < 2) {
      return 'الاسم يجب أن يكون حرفين على الأقل'; // Name must be at least 2 characters
    }

    // Allow Arabic and English letters, spaces, and common name characters
    final nameRegex = RegExp(r'^[\u0600-\u06FFa-zA-Z\s\-\.]+$');
    if (!nameRegex.hasMatch(name.trim())) {
      return 'الاسم يجب أن يحتوي على أحرف فقط'; // Name must contain only letters
    }

    return null;
  }

  /// Validates minimum length
  /// Returns null if valid, error message if invalid
  static String? validateMinLength(String? value, int minLength, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب'; // Field is required
    }

    if (value.length < minLength) {
      return '$fieldName يجب أن يكون $minLength أحرف على الأقل'; // Field must be at least X characters
    }

    return null;
  }

  /// Validates maximum length
  /// Returns null if valid, error message if invalid
  static String? validateMaxLength(String? value, int maxLength, String fieldName) {
    if (value != null && value.length > maxLength) {
      return '$fieldName يجب أن يكون $maxLength أحرف كحد أقصى'; // Field must be at most X characters
    }

    return null;
  }

  /// Validates numeric input
  /// Returns null if valid, error message if invalid
  static String? validateNumeric(String? value, String fieldName) {
    if (value == null || value.isEmpty) {
      return '$fieldName مطلوب'; // Field is required
    }

    if (double.tryParse(value) == null) {
      return '$fieldName يجب أن يكون رقم صحيح'; // Field must be a valid number
    }

    return null;
  }

  /// Validates URL format
  /// Returns null if valid, error message if invalid
  static String? validateUrl(String? url) {
    if (url == null || url.isEmpty) {
      return 'الرابط مطلوب'; // URL is required
    }

    final urlRegex = RegExp(
      r'^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$',
    );

    if (!urlRegex.hasMatch(url)) {
      return 'يرجى إدخال رابط صحيح'; // Please enter a valid URL
    }

    return null;
  }
}
