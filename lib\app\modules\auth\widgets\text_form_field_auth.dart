import 'package:flutter/material.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/core/utils/accessibility_utils.dart';

/// Customizable text form field widget for authentication screens
/// Provides consistent styling and validation for login/register forms
class TextFormFieldAuth extends StatelessWidget {
  const TextFormFieldAuth({
    super.key,
    required this.labelText,
    this.prefixIcon,
    this.obscureText = false,
    this.onPressed,
    this.suffixIcon,
    this.controller,
    this.validator,
  });
  
  // Form field configuration properties
  final String labelText; // Label text displayed in the field
  final Widget? prefixIcon; // Icon displayed at the start of field
  final bool? obscureText; // Whether to hide text (for passwords)
  final void Function()? onPressed; // Callback for suffix icon press
  final Widget? suffixIcon; // Icon displayed at the end of field
  final TextEditingController? controller; // Text input controller
  final String? Function(String?)? validator; // Validation function
  
  @override
  Widget build(BuildContext context) {
    return AccessibilityUtils.accessibleTextField(
      label: labelText,
      isRequired: true,
      isPassword: obscureText ?? false,
      child: TextFormField(
      validator: validator,
      controller: controller,
      obscureText: obscureText!,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.zero,
        suffixIcon: suffixIcon,
        prefixIcon: prefixIcon,
        labelText: labelText,
        // Consistent blue border styling for all states
        enabledBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColor.blueBorderColor),
        ),
        errorBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColor.blueBorderColor),
        ),
        border: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColor.blueBorderColor),
        ),
        focusedBorder: const OutlineInputBorder(
          borderSide: BorderSide(color: AppColor.blueBorderColor),
        ),
      ),
    ),
  );
  }
}
