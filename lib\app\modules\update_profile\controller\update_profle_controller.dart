import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for handling user and owner profile updates
/// Manages form inputs, image selection, and profile update operations
class UpdateProfleController extends GetxController {
  // Form input controllers for profile fields
  TextEditingController nameController = TextEditingController();           // User name input
  TextEditingController emailController = TextEditingController();          // Email address input
  TextEditingController phoneNumberController = TextEditingController();    // Phone number input
  TextEditingController passwordController = TextEditingController();       // Password input
  TextEditingController confirmPasswordController = TextEditingController(); // Password confirmation

  // Image selection state
  var selectedImagePath = ''.obs;                                           // Path to selected profile image

  // Image picker instance for photo selection
  final ImagePicker _imagePicker = ImagePicker();

  /// Opens gallery to select a profile image
  /// Updates selectedImagePath with chosen image or shows error if none selected
  Future<void> pickOneImage() async {
    try {
      // Open gallery for image selection
      final pickedImage =
          await _imagePicker.pickImage(source: ImageSource.gallery);
      if (pickedImage != null) {
        selectedImagePath.value = pickedImage.path; // Store selected image path
      } else {
        Get.snackbar('Error', 'No Image Selected'); // Show error if no image chosen
      }
    } catch (e) {
      Get.snackbar('Error', e.toString()); // Handle any picker errors
    }
  }

  /// Updates regular user profile with form data and optional image
  /// Sends multipart form data to API and handles success/error responses
  Future<void> updateUserProfile() async {
    // Convert selected image to multipart file for upload
    final multipartFile = await convertFileToMultipartFile();

    // Prepare form data with user input fields
    final formData = FormData({
      'name': nameController.text,
      'email': emailController.text,
      'phone_number': phoneNumberController.text,
      'password': passwordController.text,
      'confirmPassword': confirmPasswordController.text,
    });

    // Add image file to form data if one was selected
    if (multipartFile != null) {
      formData.files.add(
        MapEntry(
          'img',
          multipartFile,
        ),
      );
    }

    try {
      // Call API to update user profile
      final response =
          await RemoteServicesImpl().updateUserProfile(body: formData);

      if (response["status"] == true) {
        // Show success message and navigate back
        Get.snackbar(
          "Success",
          "Profile updated user successfully",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Refresh profile data and navigate to home after brief delay
        Future.delayed(const Duration(milliseconds: 100), () {
          Get.find<NavbarController>().selectedIndex.value = 0;
          Get.find<ProfileController>().getUserProfile();
          update();
          Get.toNamed(Routes.navBarView);
        });
      } else if (response["status"] == false) {
        // Show error message for failed update
        Get.snackbar(
          "Error status",
          "Failed to update profile. Please try again.",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any network or processing errors
      Get.snackbar(
        "Error",
        "An error occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  /// Converts selected image file to MultipartFile for API upload
  /// Returns null if no image is selected
  Future<MultipartFile?> convertFileToMultipartFile() async {
    if (selectedImagePath.isNotEmpty) {
      // Create multipart file from selected image path
      return MultipartFile(
        selectedImagePath.value,
        filename: selectedImagePath.value.split('/').last, // Extract filename from path
      );
    }
    return null; // No image selected
  }

  /// Updates business owner profile with form data and optional image
  /// Sends multipart form data to API and handles success/error responses
  Future<void> updateOwnerProfile() async {
    // Convert selected image to multipart file for upload
    final multipartFile = await convertFileToMultipartFile();

    // Prepare form data with owner input fields
    final formData = FormData({
      'name': nameController.text,
      'email': emailController.text,
      'phone_number': phoneNumberController.text,
      'password': passwordController.text,
      'confirmPassword': confirmPasswordController.text,
    });

    // Add image file to form data (required for owner profiles)
    if (multipartFile == null) {
      Get.snackbar('Error', 'No Image Selected');
    } else {
      formData.files.add(MapEntry('img', multipartFile));
    }

    try {
      // Call API to update owner profile
      final response =
          await RemoteServicesImpl().updateOwnerProfile(body: formData);

      if (response["status"] == true) {
        // Show success message and navigate back
        Get.snackbar(
          "Success",
          "Profile updated owner successfully",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green,
          colorText: Colors.white,
        );

        // Refresh owner profile data and navigate to home after brief delay
        Future.delayed(const Duration(milliseconds: 100), () {
          Get.find<NavbarController>().selectedIndex.value = 0;
          Get.find<ProfileController>().getOwnerProfile();
          update();
          Get.toNamed(Routes.navBarView);
        });
      } else if (response["status"] == false) {
        // Show error message for failed update
        Get.snackbar(
          "Error status",
          "Failed to update owner profile. Please try again.",
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      // Handle any network or processing errors
      Get.snackbar(
        "Error",
        "An error owner occurred: $e",
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }
}
