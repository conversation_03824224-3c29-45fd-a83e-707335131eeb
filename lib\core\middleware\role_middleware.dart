import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/main.dart';

/// Role-based middleware for handling owner vs regular user access
/// Ensures users can only access features appropriate to their role
class RoleMiddleware extends GetMiddleware {
  @override
  int? get priority => 2; // Lower priority than auth middleware

  /// Checks user role and redirects if accessing inappropriate features
  @override
  RouteSettings? redirect(String? route) {
    final token = box.read('token');
    final isOwner = box.read('isOwner') == 1;
    final isAuthenticated = token != null && token.toString().isNotEmpty;

    // Only apply role checks for authenticated users
    if (!isAuthenticated) {
      return null;
    }

    // Routes that are only for business owners
    final ownerOnlyRoutes = [
      Routes.updatePlace,
      // Add other owner-only routes here
    ];

    // If regular user tries to access owner-only route
    if (ownerOnlyRoutes.contains(route) && !isOwner) {
      // Redirect to main navigation
      return const RouteSettings(name: Routes.navBarView);
    }

    return null;
  }

  /// Log role-based access attempts
  @override
  GetPage? onPageCalled(GetPage? page) {
    final isOwner = box.read('isOwner') == 1;
    debugPrint('RoleMiddleware: ${isOwner ? 'Owner' : 'User'} accessing ${page?.name}');
    return super.onPageCalled(page);
  }
}
