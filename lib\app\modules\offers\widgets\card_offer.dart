import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_offers/controller/delete_offer_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/my_offers_controller.dart';
import 'package:ma3ak/app/modules/my_offers/controller/update_offer_controller.dart';
import 'package:ma3ak/app/modules/my_offers/views/update_offer_screen.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/offers/models/offer_model.dart';
import 'package:ma3ak/app/modules/offers/widgets/icon_button_outlined_widget.dart';
import 'package:ma3ak/app/modules/offers/widgets/icon_button_outlined_with_text_widget.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';

// Card widget for displaying individual offer information with actions
class CardOffer extends StatelessWidget {
  const CardOffer({
    super.key,
    required this.offer,
  });

  final OfferDatum offer; // Offer data to display

  // Builds the offer card with image, details, and action buttons
  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        // color: Colors.amber,
        borderRadius: BorderRadius.circular(10.r),
        border: Border.all(color: Colors.grey.withOpacity(.5)),
      ),
      child: Column(
        children: [
          // Image section with overlay elements
          Stack(
            children: [
              Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(10.r),
                    border: Border.all(color: Colors.grey.withOpacity(.5)),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10.r),
                    child: CachedNetworkImage(
                        fit: BoxFit.cover,
                        height: 200.h,
                        width: double.infinity,
                        imageUrl: ApiList.baseUrl + offer.img,
                        progressIndicatorBuilder: (context, url, progress) =>
                            const Center(
                              child: CircularProgressIndicator(),
                            )),
                  )),
              Align(
                alignment: Alignment.topLeft,
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: 10.w,
                    vertical: 5.h,
                  ),
                  decoration: BoxDecoration(
                      color: AppColor.blueColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10.r),
                        bottomRight: Radius.circular(4.r),
                      )
                      // circular(10.r),
                      ),
                  child: Text(
                    offer.type,
                    style: const TextStyle(color: Colors.white),
                  ),
                ),
              )
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                offer.title,
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18.sp),
              ),
              Text(
                offer.des,
              ),
            ],
          ),
          SizedBox(
            height: 10.h,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  IconButtonOutlinedWithTextWidget(
                    number: offer.likes,
                    onPressed:
                        box.read('token') == '' || box.read('token') == null
                            ? () {
                                Get.snackbar(
                                  'ليس لديك صلاحية لتقيم العرض',
                                  'الرجاء تسجيل الدخول أولاً',
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                  icon: const Icon(
                                    Icons.warning,
                                    color: Colors.white,
                                  ),
                                );
                              }
                            : box.read('isOwner') == 1
                                ? null
                                : () async {
                                    await Get.find<OffersController>()
                                        .likeOffer(slugOffer: offer.slug);
                                    if (Get.find<OffersController>()
                                            .selectedType
                                            .value !=
                                        '') {
                                      await Get.find<OffersController>()
                                          .fetchOffersByType(
                                              type: Get.find<OffersController>()
                                                  .selectedType
                                                  .value);
                                    } else {
                                      await Get.find<OffersController>()
                                          .fetchOffers();
                                    }
                                  },
                    icon: Icons.thumb_up_alt_outlined,
                  ),
                  const SizedBox(
                    width: 10,
                  ),
                  IconButtonOutlinedWithTextWidget(
                    number: offer.dislikes,
                    onPressed:
                        box.read('token') == '' || box.read('token') == null
                            ? () {
                                Get.snackbar(
                                    'ليس لديك صلاحية لتقيم العرض',
                                  'الرجاء تسجيل الدخول أولاً',
                                  backgroundColor: Colors.green,
                                  colorText: Colors.white,
                                  icon: const Icon(
                                    Icons.warning,
                                    color: Colors.white,
                                  ),
                                );
                              }
                            : box.read('isOwner') == 1
                                ? null
                                : () async {
                                    await Get.find<OffersController>()
                                        .disLikeOffer(slugOffer: offer.slug);
                                    if (Get.find<OffersController>()
                                            .selectedType
                                            .value !=
                                        '') {
                                      await Get.find<OffersController>()
                                          .fetchOffersByType(
                                              type: Get.find<OffersController>()
                                                  .selectedType
                                                  .value);
                                    } else {
                                      await Get.find<OffersController>()
                                          .fetchOffers();
                                    }
                                  },
                    icon: Icons.thumb_down_alt_outlined,
                  ),
                ],
              ),
              box.read('isOwner') == 0 || box.read('isOwner') == null
                  ? const SizedBox()
                  : Row(
                      children: [
                        IconButtonOutlinedWidget(
                          onPressed: () async {
                            final updateOfferController =
                                Get.put(UpdateOfferController());
                            updateOfferController.titleController.text =
                                offer.title;
                            updateOfferController.desController.text =
                                offer.des;
                            updateOfferController.typeController.text =
                                offer.type;
                            updateOfferController.image.value = offer.img;
                            updateOfferController.slugOffer.value = offer.slug;
                            updateOfferController.selectedType.value =
                                offer.type;
                            Get.to(() => UpdateOfferScreen());
                          },
                          icon: Icons.edit,
                        ),
                        IconButtonOutlinedWidget(
                          onPressed: () async {
                            await Get.find<DeleteOfferController>()
                                .deleteOffer(slugOffer: offer.slug);
                            await Get.find<MyOffersController>()
                                .fetchMyOffersOffers();
                            await Get.find<OffersController>().fetchOffers();
                          },
                          icon: Icons.delete,
                        ),
                      ],
                    ),
            ],
          )
        ],
      ),
    );
  }
}
