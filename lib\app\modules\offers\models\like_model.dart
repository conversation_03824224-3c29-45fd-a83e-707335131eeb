/// Model class for handling like/unlike operations response
class LikeModel {
  // Response status indicating success/failure of like operation
  final bool? status;
  
  // Response message providing additional context or error details
  final String? message;

  // Default constructor with optional parameters
  LikeModel({ this.status,  this.message});

  // Factory constructor to create LikeModel from API JSON response
  factory LikeModel.fromJson(Map<String, dynamic> json) {
    return LikeModel(
      status: json['status'],
      message: json['message'],
    );
  }

  // Converts LikeModel instance to JSON for API requests
  Map<String, dynamic> toJson() {
    return {
      'status': status,
      'message': message,
    };
  }
}
