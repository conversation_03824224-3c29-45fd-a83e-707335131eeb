import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/offers/models/offer_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for managing user's own offers and search functionality
class MyOffersController extends GetxController {
  // Loading state indicator for API requests
  var isLoading = false.obs;

  // Observable container for user's offers data
  final offerData = OfferModel().obs;

  // Search input controller for filtering offers
  TextEditingController searchController = TextEditingController();

  // Observable list for filtered search results
  RxList<OfferDatum>? searchResult = <OfferDatum>[].obs;

  /// Fetches all offers created by the current user
  Future<void> fetchMyOffersOffers() async {
    isLoading(true);

    // Call API service to retrieve user's offers
    final data = await RemoteServicesImpl()
        .fetchMyOffersOffers();
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      offerData.value = dataModel;
      searchResult?.value = dataModel.data!;
      debugPrint(dataModel.toString());
    });
  }

  /// Filters offers based on search text input
  void searchOffers(String text) {
    if (text.isEmpty) {
      // Show all offers when search is empty
      searchResult?.value = offerData.value.data!;
    } else {
      // Filter offers by title containing search text
      searchResult?.value = offerData.value.data!
          .where(
            (element) =>
                element.title.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
    }
  }

  /// Initialize controller by fetching user's offers on startup
  @override
  void onInit() async {
    await fetchMyOffersOffers();
    super.onInit();
  }
}
