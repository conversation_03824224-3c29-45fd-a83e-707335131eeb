import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/views/login_screen.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';
import 'package:ma3ak/app/modules/place/views/place_screen_owner.dart';
import 'package:ma3ak/app/modules/place/views/place_screen.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';

class FavoriteScreen extends StatelessWidget {
  FavoriteScreen({super.key});

  final FavoriteController favoriteController = Get.put(FavoriteController());

  @override
  Widget build(BuildContext context) {
    String? token = box.read('token');
    if (token == null || token.isEmpty) {
      // Navigate to the LoginScreen if the token is null or empty
      Future.microtask(() {
        Get.to(() => const LoginScreen());
        Get.find<NavbarController>().selectedIndex.value = 2;
      });
      return Container(); // Return an empty container while navigating
    }

    return   RefreshIndicator(
      onRefresh: () async {
        favoriteController.getFavoriteUser();
      },
      child: ListView(
        children: [
          const MainAppBar(
            title: Text('الأماكن المفضلة'),
          ),
          SingleChildScrollView(
            child: Obx(() {
              var fav = favoriteController.favoritePlaceList;
              if (favoriteController.isLoading.value) {
                return const Center(
                  child: CircularProgressIndicator(),
                );
              } else if (fav?.isEmpty ?? true) {
                return const Center(
                  child: Text('لا يوجد مكانات مفضلة'),
                );
              }
              return GridView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                ),
                itemCount: fav?.length ?? 0,
                itemBuilder: (context, index) {
                  var cardFav = fav?[index];
                  return InkWell(
                    onTap: () async {
                       Get.dialog(
            Center(
              child: Container(
                decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
                color: AppColor.primaryColor,
                ),
                height: 100.h, // Adjust height to your desired value
                width: 100.w,   // Adjust width to your desired value
                child: const Center(child: CircularProgressIndicator(color: Colors.white,),),
              ),
            ),
            barrierColor: Colors.transparent,
          );
                      PlaceController placeController =
                          Get.put(PlaceController());
          
                      var res = await placeController.getPlaceInforamtion(
                          slugPlace: cardFav.slug);
          
                      res.fold((error) {
                        // Dismiss the loading dialog after data is fetched
                        Get.back();
                        debugPrint(error.toString());
                      }, (dataModel) async {
                        await Get.find<OffersController>()
                            .fetchOffersByPlace(slugPlace: cardFav.slug);
                        placeController.placeInfromationOld.value = dataModel;
                        // Dismiss the loading dialog after data is fetched
                        Get.back();
                        if (box.read('isOwner') == 1) {
                          Get.to(() => PlaceScreenOwner(
                                placeInfromation:
                                    placeController.placeInfromationOld,
                              ));
                        } else {
                          Get.to(() => PlaceScreen(
                              placeInfromation:
                                  placeController.placeInfromationOld));
                        }
                      });
                    },
                    child: Card(
                        child: Stack(
                      children: [
                        CachedNetworkImage(
                          errorWidget: (context, url, error) {
                            return const Center(
                              child: Text('لا يوجد صورة'),
                            );
                          },
                          fit: BoxFit.cover,
                          imageUrl: ApiList.baseUrl + cardFav!.coverImage,
                          imageBuilder: (context, imageProvider) => Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: Colors.grey,
                              image: DecorationImage(
                                fit: BoxFit.cover,
                                image: imageProvider,
                              ),
                            ),
                          ),
                        ),
                        IconButton.outlined(
                            style: ButtonStyle(
                                backgroundColor: WidgetStatePropertyAll<Color>(
                              Colors.white.withOpacity(.6),
                            )),
                            onPressed: () {
                              favoriteController.addOrRemoveFavorite(
                                  slugPlace: cardFav.slug);
                            },
                            icon: const Icon(
                              Icons.favorite,
                              color: Colors.redAccent,
                            )),
                        Center(
                            child: Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 4),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.r),
                            color: Colors.white.withOpacity(.6),
                          ),
                          child: Text(
                            cardFav.name,
                            style: const TextStyle(fontWeight: FontWeight.bold),
                          ),
                        )),
                      ],
                    )),
                  );
                },
              );
            }),
          ),
        ],
      ),
    );
  }
}
