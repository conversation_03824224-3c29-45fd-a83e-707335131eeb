import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:ma3ak/config/theme/app_color.dart';

class IconButtonOutlinedWidget extends StatelessWidget {
  const IconButtonOutlinedWidget({
    super.key,
    this.icon = Icons.handshake, this.onPressed,
  });
  final IconData? icon;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return IconButton.outlined(
        style: ButtonStyle(
            side: const WidgetStatePropertyAll<BorderSide>(
              BorderSide(
                color: AppColor.primaryColor,
              ),
            ),
            shape: WidgetStatePropertyAll(RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(6.r),
            ))),
        onPressed: onPressed,
        icon: Icon(icon));
  }
}
