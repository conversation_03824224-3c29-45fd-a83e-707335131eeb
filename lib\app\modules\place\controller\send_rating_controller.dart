import 'package:dartz/dartz.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/rating_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for handling place rating submission functionality
class SendRatingController extends GetxController {
  // Loading state indicator for rating submission
  var isLoading = false.obs;

  // Observable rating value (0.0 to 5.0)
  var rating = 0.0.obs;

  /// Submits user rating for a specific place
  Future<Either<String, RatingModel>> userRating(
      {required String slugPlace, required double rating}) async {
    // Prepare request body with rating value
    final Map<String, dynamic> body = {"stars_rated": rating.toDouble()};

    isLoading(true);

    // Call API service to submit the rating
    final data = await RemoteServicesImpl().userRating(
        slugPlace: slugPlace, body: body);
    isLoading(false);

    // Handle API response and show appropriate feedback
    data.fold((error) {
      Get.snackbar('فشل', 'فشل التقييم');
      debugPrint(error.toString());
    }, (dataModel) {
      if (dataModel.status == true) {
        Get.snackbar('الرسالة', 'تم التقييم بنجاح');
      } else {
        Get.snackbar('الرسالة', dataModel.message.toString());
      }
    });

    return data;
  }
}
