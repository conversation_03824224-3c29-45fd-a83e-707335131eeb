import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:shimmer/shimmer.dart';

/// Shimmer loading widget for horizontal offers/categories section
/// Displays 10 square shimmer placeholders in a horizontal scrollable list
class OffersSectionShimmer extends StatelessWidget {
  const OffersSectionShimmer({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        SizedBox(
          child: SizedBox(
            width: double.infinity,
            height: 90.h,
            // Horizontal scrollable list of shimmer placeholders
            child: ListView.builder(
                padding: EdgeInsets.only(left: 16.w),
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: 10, // Show 10 placeholder items
                itemBuilder: (BuildContext context, index) {
                  return Shimmer.fromColors(
                    baseColor: Colors.grey[200]!,
                    highlightColor: Colors.grey[300]!,
                    child: Padding(
                      padding: EdgeInsets.only(right: 12.w),
                      // Square shimmer container for category/offer placeholder
                      child: Container(
                        height: 90.h,
                        width: 90.w,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16.r),
                          color: Colors.white,
                        ),
                      ),
                    ),
                  );
                }),
          ),
        ),
      ],
    );
  }
}
