import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/svg_icon.dart';

class ListTileDrawerWidget extends StatelessWidget {
  const ListTileDrawerWidget({
    super.key,
    this.svgIcon = SvgIcon.cardProfile,
    required this.title,
    this.onTap,
  });
  final String? svgIcon;
  final String title;
  final void Function()? onTap;
  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: SvgPicture.asset(svgIcon!,
          height: 20,
          width: 20,
          colorFilter:
              const ColorFilter.mode(AppColor.blackColor, BlendMode.srcIn)),
      title: Text(
        title,
        style: const TextStyle(fontWeight: FontWeight.bold),
      ),
      onTap: onTap,
      trailing: const Icon(
        Icons.arrow_forward_ios,
        size: 15,
        color: AppColor.blueColor1,
      ),
    );
  }
}
