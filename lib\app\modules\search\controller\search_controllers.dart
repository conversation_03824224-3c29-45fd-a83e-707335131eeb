import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/ctegoreis_model.dart';
import 'package:ma3ak/app/modules/place/models/sections_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

// Controller for managing place search functionality and filtering
class SearchPlaceController extends GetxController {
  // Loading states and data models
  var isLoading = true.obs;
  Rx<SectionsModel> sectionsData = SectionsModel().obs;
  Rx<CategoriesModel>? categoriesData = CategoriesModel().obs;
  Rx<int?> selectedSection = Rx<int?>(null);
  RxList<Place> allPlaces = RxList<Place>([]);
  RxList<Place>? resultAllPlaces = RxList<Place>([]);
  
  // Search and UI state variables
  TextEditingController searchController = TextEditingController();
  RxBool isClearSections = true.obs;

  // Filters places based on search text input
  void searchPlaces(String text) {
    // If categories exist, gather all places from them

    if (text.isEmpty) {
      resultAllPlaces?.value = allPlaces;
    } else {
      resultAllPlaces?.value = allPlaces
          .where(
            (element) =>
                element.name.toLowerCase().contains(text.toLowerCase()),
          )
          .toList();
    }
  }

  // Fetches all available sections from API
  Future<void> fetchSections() async {
    isLoading(true);
    final data = await RemoteServicesImpl()
        .fetchSections(); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      sectionsData.value = dataModel;
    });
  }

  // Handles section selection change and fetches related places
  void onSectionChanged(int? newValue) {
    // Set the selected section to the new value
    selectedSection.value = newValue;
    isClearSections.value = false;

    // Find the slug for the selected section
    final selectedSectionData = sectionsData.value.data?.firstWhere(
      (section) => section.id == newValue,

      // orElse: () => null, // Return null if no matching section is found
    );

    // If a section was found, call the function with the slug
    if (selectedSectionData != null) {
      // You can  now pass the `slug` to the function
      fetchCategoriesAndPlaceBySection(slugSecetion: selectedSectionData.slug!);
    } else {
      // Handle case where the section wasn't found (optional)
      debugPrint("Section not found!");
    }
  }

  // Fetches categories and places for a specific section
  Future<void> fetchCategoriesAndPlaceBySection(
      {required String slugSecetion}) async {
    isLoading(true);
    final data = await RemoteServicesImpl().fetchCategoriesAndPlaceBySection(
        slugScetion: slugSecetion); // Make sure this method exists
    isLoading(false);
    data.fold((error) {
      debugPrint(error.toString());
    }, (dataModel) {
      categoriesData?.value = dataModel;
      final categoriesCards = categoriesData?.value.data;
      allPlaces.clear();
      if (categoriesCards != null) {
        for (var category in categoriesCards) {
          allPlaces.addAll(category.places);
        }
        resultAllPlaces?.value = allPlaces;
      }
    });
  }

  // Initializes controller by fetching sections data
  @override
  void onInit() {
    fetchSections();
    super.onInit();
  }
}
