import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/place/models/semd_comment_model.dart';
import 'package:ma3ak/data/api/remote_services_impl.dart';

/// Controller for handling offer deletion functionality
/// Manages the deletion process and loading states for offers
class DeleteOfferController extends GetxController {
  // Loading state indicator for delete operation
  var isLoading = false.obs;

  // Response model container (currently unused but available for future use)
  Rx<SendCommentModel>? sendCommentModel = SendCommentModel().obs;

  /// Deletes an offer by its slug identifier
  /// Shows loading state during API call and handles success/error responses
  Future<void> deleteOffer({required String slugOffer}) async {
    // Set loading state to true during API call
    isLoading(true);

    // Call API to delete the offer
    final data = await RemoteServicesImpl()
        .deleteOffer(slugOffer: slugOffer);

    // Reset loading state after API call completes
    isLoading(false);

    // Handle API response using Either pattern
    data.fold((error) {
      debugPrint(error.toString()); // Log error if deletion fails
    }, (dataModel) {
      debugPrint(dataModel.toString()); // Log success response
    });
  }
}
