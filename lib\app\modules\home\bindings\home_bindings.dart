import 'package:get/get.dart';
import 'package:ma3ak/app/modules/favorite/controller/favorite_controller.dart';
import 'package:ma3ak/app/modules/home/<USER>/ads_controller.dart';
import 'package:ma3ak/app/modules/offers/controller/offers_controller.dart';
import 'package:ma3ak/app/modules/place/controller/comments_controller.dart';
import 'package:ma3ak/app/modules/place/controller/place_controller.dart';

/// Dependency injection bindings for home module controllers
/// Registers all controllers needed for home screen functionality
class HomeBindings extends Bindings {
  @override
  void dependencies() {
    // Register core feature controllers for home module
    Get.lazyPut(() => AdsController());        // Advertisement management
    Get.lazyPut(() => PlaceController());      // Place data operations
    Get.lazyPut(() => OffersController());     // Offers and deals management
    Get.lazyPut(() => FavoriteController());   // User favorites functionality
    Get.lazyPut(() => CommentsController());   // Comments and reviews system
  }
}
