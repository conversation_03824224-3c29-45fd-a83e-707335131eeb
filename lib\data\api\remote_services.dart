import 'package:dartz/dartz.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:ma3ak/app/modules/dashboard/models/dashboard_model.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/app/modules/profile/models/logout_account_model.dart';
import 'package:ma3ak/app/modules/update_place/models/update_place_model.dart';

// Abstract interface defining remote API service methods
abstract class RemoteServices extends GetxController {
  // Authentication and session management
  Future<Either<String, LogoutAccountModel>> logoutUser();
  Future<Either<String, LogoutAccountModel>> logoutOwner();
  
  // Dashboard and analytics data
  Future<Either<String, DashboardModel>> fetchDashboard();
  
  // Place management operations
  Future<Either<String, UpdatePlaceModel>> updatePlace(
      {required dynamic body, required String slugPlace});
  Future<Either<String, PlaceModel>> fetchGreaterRatePlace();
}
