import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/my_place/controller/my_place_controller.dart';
import 'package:ma3ak/app/modules/offers/widgets/text_form_field_offer_widget.dart';
import 'package:ma3ak/app/modules/place/models/place_model.dart';
import 'package:ma3ak/app/modules/update_place/controller/update_place_controller.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/widgets/basic_button.dart';

class UpdatePlaceScreen extends StatelessWidget {
  UpdatePlaceScreen({super.key});
  final UpdatePlaceController updatePlaceController =
      Get.put(UpdatePlaceController());

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('UpdatePlaceScreen'),
        ),
        // final String coverImage;
        body: SafeArea(
          child: Obx(() {
            if (updatePlaceController.isloading.value) {
              return const Center(child: CircularProgressIndicator());
            }
            var placeCard = Get.arguments as PlaceDatum;

            // Update controllers if they are empty
            updatePlaceController.nameController.text =
                updatePlaceController.nameController.text.isEmpty
                    ? placeCard.name.toString()
                    : updatePlaceController.nameController.text;

            updatePlaceController.governorController.text =
                updatePlaceController.governorController.text.isEmpty
                    ? placeCard.governor.toString()
                    : updatePlaceController.governorController.text;

            updatePlaceController.directorateController.text =
                updatePlaceController.directorateController.text.isEmpty
                    ? placeCard.directorate.toString()
                    : updatePlaceController.directorateController.text;

            updatePlaceController.addressDetailsController.text =
                updatePlaceController.addressDetailsController.text.isEmpty
                    ? placeCard.addressDetails.toString()
                    : updatePlaceController.addressDetailsController.text;

            updatePlaceController.whatsappNumberController.text =
                updatePlaceController.whatsappNumberController.text.isEmpty
                    ? placeCard.whatsappNumber.toString()
                    : updatePlaceController.whatsappNumberController.text;

            updatePlaceController.desController.text =
                updatePlaceController.desController.text.isEmpty
                    ? placeCard.des.toString()
                    : updatePlaceController.desController.text;
            updatePlaceController.desController.text =
                updatePlaceController.desController.text.isEmpty
                    ? placeCard.des.toString()
                    : updatePlaceController.desController.text;
            return SingleChildScrollView(
              child: Column(
                children: [
                  Stack(
                    children: [
                      Obx(() {
                        return updatePlaceController
                                .selectedImagePath.value.isEmpty
                            ? Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: CachedNetworkImage(
                                  fit: BoxFit.cover,
                                  height:
                                      MediaQuery.of(context).size.height * .2,
                                  width: double.infinity,
                                  imageUrl:
                                      ApiList.baseUrl + placeCard.coverImage,
                                ),
                              )
                            : Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 16),
                                child: Container(
                                    height: MediaQuery.of(context).size.height *
                                        .25,
                                    width: MediaQuery.of(context).size.width,
                                    decoration: BoxDecoration(
                                      color: Colors.grey.withOpacity(.5),
                                      borderRadius: BorderRadius.circular(8),
                                      image: DecorationImage(
                                          image: FileImage(File(
                                              updatePlaceController
                                                  .selectedImagePath.value)),
                                          fit: BoxFit.cover),
                                    )),
                              );
                      }),
                      Positioned(
                        bottom: 0,
                        left: 16,
                        child: IconButton.filled(
                          onPressed: () async {
                            await updatePlaceController.pickOneImage();
                          },
                          icon: const Icon(Icons.image_search),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.nameController,
                    labelText: 'اسم المكان',
                    hintText: 'ادخل اسم المكان',
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.governorController,
                    labelText: 'المحافظة',
                    hintText: 'ادخل المحافظة',
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.directorateController,
                    labelText: 'المديرية',
                    hintText: 'ادخل المديرية',
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.addressDetailsController,
                    labelText: 'العنوان',
                    hintText: 'ادخل العنوان',
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.whatsappNumberController,
                    labelText: 'رقم الواتساب',
                    hintText: 'ادخل رقم الواتساب',
                  ),
                  TextFormFieldOfferWidget(
                    controller: updatePlaceController.desController,
                    labelText: 'الوصف',
                    hintText: 'ادخل الوصف',
                  ),
                  BasicButton(
                      onPressed: () async {
                        await updatePlaceController
                            .updatePlace(placeCard.slug.toString());
                        await Get.find<MyPlaceController>().fetchMyPlace();
                        Get.back();
                      },
                      wdth: MediaQuery.of(context).size.width * .9,
                      text: 'تحديث')
                ],
              ),
            );
          }),
        ));
  }
}
