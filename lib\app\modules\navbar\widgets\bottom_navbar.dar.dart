  import 'package:flutter/material.dart';
  import 'package:flutter_svg/svg.dart';
  import 'package:ma3ak/config/theme/app_color.dart';
  import 'package:ma3ak/core/utils/accessibility_utils.dart';

  // Custom bottom navigation item widget with icon and text
  class BottomNavItem extends StatelessWidget {
    final String? imageData; // SVG icon path
    final String? tittle; // Display text
    final VoidCallback? onTap; // Tap callback function
    final bool isSelected; // Selection state for styling
    const BottomNavItem(
        {super.key,
        this.imageData,
        this.tittle,
        this.onTap,
        this.isSelected = false});

    // Builds the navigation item with responsive styling
    @override
    Widget build(BuildContext context) {
      return Expanded(
        child: AccessibilityUtils.accessibleNavigationItem(
          label: tittle ?? '',
          onTap: onTap ?? () {},
          isSelected: isSelected,
          child: InkWell(
            onTap: onTap,
            child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // SVG icon with conditional coloring based on selection
                SvgPicture.asset(
                  imageData!,
                  colorFilter: ColorFilter.mode(
                      isSelected
                          ? AppColor.primaryColor
                          : AppColor.deSelectedColor,
                      BlendMode.srcIn),
                  height: 20,
                  width: 20,
                ),
                // Text label with conditional styling based on selection
                Padding(
                  padding: const EdgeInsets.only(top: 4, bottom: 2),
                  child: Text(
                    overflow: TextOverflow.ellipsis,
                    tittle!,
                    maxLines: 1,
                    style: TextStyle(
                      fontSize: 12,
                      color: isSelected
                          ? AppColor.primaryColor
                          : AppColor.deSelectedColor,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
    }
  }
