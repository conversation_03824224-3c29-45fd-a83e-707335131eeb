import 'dart:convert';

// To parse this JSON data, do
//
//     final categoriesModel = categoriesModelFromJson(jsonString);

CategoriesModel categoriesModelFromJson(String str) =>
    CategoriesModel.fromJson(json.decode(str));

String categoriesModelToJson(CategoriesModel data) => json.encode(data.toJson());

class CategoriesModel {
  final bool? status;
  final String? message;
  final List<Category>? data;

  CategoriesModel({
    this.status,
    this.message,
    this.data,
  });

  factory CategoriesModel.fromJson(Map<String, dynamic> json) => CategoriesModel(
        status: json["status"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : List<Category>.from(json["data"].map((x) => Category.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "data": data == null
            ? null
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Category {
  final int id;
  final String name;
  final String img;
  final int status;
  final String slug;
  final int sectionId;
  final List<Place> places;

  Category({
    required this.id,
    required this.name,
    required this.img,
    required this.status,
    required this.slug,
    required this.sectionId,
    required this.places,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        name: json["name"],
        img: json["img"],
        status: json["status"],
        slug: json["slug"],
        sectionId: json["section_id"],
        places: json["place"] == null
            ? []
            : List<Place>.from(json["place"].map((x) => Place.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "img": img,
        "status": status,
        "slug": slug,
        "section_id": sectionId,
        "place": List<dynamic>.from(places.map((x) => x.toJson())),
      };
}

class Place {
  final int id;
  final String name;
  final String governor;
  final String directorate;
  final String addressDetails;
  final String des;
  final int whatsappNumber;
  final String coverImage;
  final String slug;
  final int stars;
  final String status;
  final int? parentId;
  final int catId;
  final int ownerId;

  Place({
    required this.id,
    required this.name,
    required this.governor,
    required this.directorate,
    required this.addressDetails,
    required this.des,
    required this.whatsappNumber,
    required this.coverImage,
    required this.slug,
    required this.stars,
    required this.status,
    this.parentId,
    required this.catId,
    required this.ownerId,
  });

  factory Place.fromJson(Map<String, dynamic> json) => Place(
        id: json["id"],
        name: json["name"],
        governor: json["governor"],
        directorate: json["directorate"],
        addressDetails: json["address_details"],
        des: json["des"],
        whatsappNumber: json["whatsapp_number"],
        coverImage: json["cover_image"],
        slug: json["slug"],
        stars: json["stars"],
        status: json["status"],
        parentId: json["parent_id"],
        catId: json["cat_id"],
        ownerId: json["owner_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "governor": governor,
        "directorate": directorate,
        "address_details": addressDetails,
        "des": des,
        "whatsapp_number": whatsappNumber,
        "cover_image": coverImage,
        "slug": slug,
        "stars": stars,
        "status": status,
        "parent_id": parentId,
        "cat_id": catId,
        "owner_id": ownerId,
      };
}
