import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Accessibility utilities for improving app usability
/// Provides semantic labels and accessibility features for better user experience
class AccessibilityUtils {
  AccessibilityUtils._();

  /// Create semantic label for buttons
  static String getButtonSemantics({
    required String label,
    String? hint,
    bool isEnabled = true,
    bool isLoading = false,
  }) {
    String semantics = label;
    
    if (isLoading) {
      semantics += '، جاري التحميل'; // , Loading
    } else if (!isEnabled) {
      semantics += '، غير متاح'; // , Disabled
    }
    
    if (hint != null) {
      semantics += '، $hint';
    }
    
    return semantics;
  }

  /// Create semantic label for form fields
  static String getFormFieldSemantics({
    required String label,
    String? value,
    String? hint,
    String? error,
    bool isRequired = false,
    bool isPassword = false,
  }) {
    String semantics = label;
    
    if (isRequired) {
      semantics += '، مطلوب'; // , Required
    }
    
    if (isPassword) {
      semantics += '، حقل كلمة مرور'; // , Password field
    }
    
    if (value != null && value.isNotEmpty) {
      if (isPassword) {
        semantics += '، تم إدخال كلمة المرور'; // , Password entered
      } else {
        semantics += '، القيمة: $value'; // , Value: $value
      }
    }
    
    if (error != null) {
      semantics += '، خطأ: $error'; // , Error: $error
    }
    
    if (hint != null) {
      semantics += '، $hint';
    }
    
    return semantics;
  }

  /// Create semantic label for navigation items
  static String getNavigationSemantics({
    required String label,
    bool isSelected = false,
    int? index,
    int? totalItems,
  }) {
    String semantics = label;
    
    if (isSelected) {
      semantics += '، محدد'; // , Selected
    }
    
    if (index != null && totalItems != null) {
      semantics += '، العنصر ${index + 1} من $totalItems'; // , Item X of Y
    }
    
    semantics += '، زر التنقل'; // , Navigation button
    
    return semantics;
  }

  /// Create semantic label for cards and list items
  static String getCardSemantics({
    required String title,
    String? subtitle,
    String? status,
    bool isClickable = true,
  }) {
    String semantics = title;
    
    if (subtitle != null) {
      semantics += '، $subtitle';
    }
    
    if (status != null) {
      semantics += '، الحالة: $status'; // , Status: $status
    }
    
    if (isClickable) {
      semantics += '، اضغط للتفاصيل'; // , Tap for details
    }
    
    return semantics;
  }

  /// Create semantic label for icons
  static String getIconSemantics({
    required String action,
    String? description,
  }) {
    String semantics = action;
    
    if (description != null) {
      semantics += '، $description';
    }
    
    return semantics;
  }

  /// Wrap widget with semantic properties
  static Widget wrapWithSemantics({
    required Widget child,
    required String label,
    String? hint,
    String? value,
    bool? isButton,
    bool? isTextField,
    bool? isImage,
    bool? isHeader,
    bool? isLink,
    bool excludeSemantics = false,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: label,
      hint: hint,
      value: value,
      button: isButton,
      textField: isTextField,
      image: isImage,
      header: isHeader,
      link: isLink,
      excludeSemantics: excludeSemantics,
      onTap: onTap,
      child: child,
    );
  }

  /// Create accessible button wrapper
  static Widget accessibleButton({
    required Widget child,
    required String label,
    required VoidCallback? onPressed,
    String? hint,
    bool isEnabled = true,
    bool isLoading = false,
  }) {
    return Semantics(
      label: getButtonSemantics(
        label: label,
        hint: hint,
        isEnabled: isEnabled,
        isLoading: isLoading,
      ),
      button: true,
      enabled: isEnabled && !isLoading,
      onTap: isEnabled && !isLoading ? onPressed : null,
      child: child,
    );
  }

  /// Create accessible text field wrapper
  static Widget accessibleTextField({
    required Widget child,
    required String label,
    String? value,
    String? hint,
    String? error,
    bool isRequired = false,
    bool isPassword = false,
  }) {
    return Semantics(
      label: getFormFieldSemantics(
        label: label,
        value: value,
        hint: hint,
        error: error,
        isRequired: isRequired,
        isPassword: isPassword,
      ),
      textField: true,
      child: child,
    );
  }

  /// Create accessible navigation item wrapper
  static Widget accessibleNavigationItem({
    required Widget child,
    required String label,
    required VoidCallback onTap,
    bool isSelected = false,
    int? index,
    int? totalItems,
  }) {
    return Semantics(
      label: getNavigationSemantics(
        label: label,
        isSelected: isSelected,
        index: index,
        totalItems: totalItems,
      ),
      button: true,
      selected: isSelected,
      onTap: onTap,
      child: child,
    );
  }

  /// Create accessible card wrapper
  static Widget accessibleCard({
    required Widget child,
    required String title,
    String? subtitle,
    String? status,
    VoidCallback? onTap,
    bool isClickable = true,
  }) {
    return Semantics(
      label: getCardSemantics(
        title: title,
        subtitle: subtitle,
        status: status,
        isClickable: isClickable,
      ),
      button: isClickable,
      onTap: isClickable ? onTap : null,
      child: child,
    );
  }

  /// Create accessible icon wrapper
  static Widget accessibleIcon({
    required Widget child,
    required String action,
    String? description,
    VoidCallback? onTap,
  }) {
    return Semantics(
      label: getIconSemantics(
        action: action,
        description: description,
      ),
      button: onTap != null,
      onTap: onTap,
      child: child,
    );
  }

  /// Announce message to screen readers
  static void announceMessage(String message) {
    SemanticsService.announce(message, TextDirection.rtl);
  }

  /// Focus on specific widget
  static void focusWidget(FocusNode focusNode) {
    focusNode.requestFocus();
  }

  /// Check if accessibility features are enabled
  static bool isAccessibilityEnabled(BuildContext context) {
    return MediaQuery.of(context).accessibleNavigation;
  }

  /// Get text scale factor for accessibility
  static double getTextScaleFactor(BuildContext context) {
    return MediaQuery.of(context).textScaler.scale(1.0);
  }

  /// Check if high contrast is enabled
  static bool isHighContrastEnabled(BuildContext context) {
    return MediaQuery.of(context).highContrast;
  }

  /// Check if bold text is enabled
  static bool isBoldTextEnabled(BuildContext context) {
    return MediaQuery.of(context).boldText;
  }

  /// Create semantic label for dialogs
  static String getDialogSemantics({
    required String title,
    String? type,
    String? hint,
  }) {
    String semantics = title;

    if (type != null) {
      switch (type) {
        case 'confirmation':
          semantics += '، حوار تأكيد'; // , Confirmation dialog
          break;
        case 'alert':
          semantics += '، تنبيه'; // , Alert
          break;
        case 'info':
          semantics += '، معلومات'; // , Information
          break;
        default:
          semantics += '، حوار'; // , Dialog
      }
    }

    if (hint != null) {
      semantics += '، $hint';
    }

    return semantics;
  }
}
