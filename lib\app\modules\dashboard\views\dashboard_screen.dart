import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/dashboard/controller/dashboard_controller.dart';
import 'package:ma3ak/app/modules/dashboard/widgets/container_dashboard_owner_info.dart';
import 'package:ma3ak/app/modules/dashboard/widgets/container_dashboard_widget.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/app/modules/update_profile/controller/update_profle_controller.dart';
import 'package:ma3ak/app/modules/update_profile/views/update_profile_screen.dart';
import 'package:ma3ak/utils/svg_icon.dart';
import 'package:ma3ak/widgets/widgets/main_app_bar.dart';

// Dashboard screen for business owners showing analytics and statistics
class DashboardScreen extends StatelessWidget {
  DashboardScreen({super.key});
  
  // Controllers for dashboard data and profile management
  final DashboardController dashboardController =
      Get.put(DashboardController());
  final ProfileController profileController = Get.put(ProfileController());

  // Builds the dashboard with nested scroll view and statistics widgets
  @override
  Widget build(BuildContext context) {
    return NestedScrollView(
      // Header with pinned app bar
      headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
        return [
          SliverAppBar(
            pinned: true,
            floating: false,
            flexibleSpace: Container(
              color:
                  Theme.of(context).scaffoldBackgroundColor, // Background color
              child: const MainAppBar(),
            ),
          ),
        ];
      }, //RefreshIndicator
      // Main dashboard content
      body: SafeArea(
        child: SingleChildScrollView(
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            // Reactive dashboard content based on loading state
            child: Obx(() {
              var dashData = dashboardController.dashboardData.value.data;
              var ownerInfo = profileController.ownerProfileData.value.owner;
              return Column(
                children: [
                  ContainerDashboardOwnerInfo(
                    name: ownerInfo?.name,
                    city: ownerInfo?.email,
                    url: ownerInfo?.img,
                    onPressed: () {
                      Get.lazyPut(() => UpdateProfleController());
                      Get.to(() => UpdateProfileScreen());
                    },
                  ),
                  ContainerDashboardWidget(
                    onTap: () {
                      Get.find<NavbarController>().selectedIndex(1);
                    },
                    number: dashData?.offers.toString(),
                    text: 'العروض',
                    svgIcon: SvgIcon.ads,
                    textUnder: 'عدد العروض',
                  ),
                  ContainerDashboardWidget(
                    onTap: () {
                      Get.find<NavbarController>().selectedIndex(4);
                    },
                    number: dashData?.places.toString(),
                    text: 'الأماكن',
                    svgIcon: SvgIcon.ads,
                    textUnder: 'عدد الأماكن',
                  ),
                  ContainerDashboardWidget(
                    number: dashData?.visitorPlace.toString(),
                    text: 'الزيارات',
                    svgIcon: SvgIcon.ads,
                    textUnder: 'عدد الزيارات',
                  ),
                ],
              );
            }),
          ),
        ),
      ),
    );
  }
}
