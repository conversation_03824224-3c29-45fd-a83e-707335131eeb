{"buildFiles": ["C:\\src\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Flutter Application\\ma3ak\\android\\app\\.cxx\\Debug\\4c4d6a3j\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Flutter Application\\ma3ak\\android\\app\\.cxx\\Debug\\4c4d6a3j\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}