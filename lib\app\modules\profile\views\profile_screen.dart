import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/views/login_screen.dart';
import 'package:ma3ak/app/modules/navbar/controller/navbar_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/logout_account_controller.dart';
import 'package:ma3ak/app/modules/profile/controller/profile_controller.dart';
import 'package:ma3ak/app/modules/profile/widgets/text_rich_widget.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';
import 'package:ma3ak/app/modules/update_profile/controller/update_profle_controller.dart';
import 'package:ma3ak/app/modules/update_profile/views/update_profile_screen.dart';
import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/core/utils/dialog_utils.dart';
import 'package:ma3ak/data/constants/api_list.dart';
import 'package:ma3ak/main.dart';
import 'package:ma3ak/widgets/reactive_button.dart';
import 'package:ma3ak/widgets/widgets/basic_app_bar.dart';

import '../../../../widgets/widgets/main_app_bar.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key, this.isAlowBack = false});
  final bool? isAlowBack;

  @override
  Widget build(BuildContext context) {
    String? token = box.read('token');
    if (token == null || token.isEmpty) {
      // Navigate to the LoginScreen if the token is null or empty
      Future.microtask(() {
        Get.to(() => const LoginScreen());
        Get.find<NavbarController>().selectedIndex.value = 2;
      });
      return Container(); // Return an empty container while navigating
    }
    // if (box.read('token') != '') {
    //   profileController.getUserProfile();
    // }

    return GetBuilder<ProfileController>(
      builder: (profileController) {
        if (profileController.isLoading.value) {
          return const Center(child: CircularProgressIndicator());
        }
        var user = profileController.userProfileData.value.user;
        return Stack(
          children: [
            Column(
              children: [
                isAlowBack!
                    ? const BasicAppbar(backgroundColor: AppColor.primaryColor)
                    : const MainAppBar(
                        hideTitle: true,
                        ),
                Expanded(
                  child: Container(
                    color: AppColor.primaryColor,
                  ),
                ),
                Expanded(
                  child: Container(),
                ),
              ],
            ),
            Align(
              alignment: Alignment.center,
              child: Stack(
                clipBehavior:
                    Clip.none, // Allow overflow of positioned elements
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Container(
                      height: MediaQuery.of(context).size.height * .6,
                      width: double.infinity,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(30),
                        border: Border.all(
                            color: AppColor.blueBorderColor, width: .5),
                        color: AppColor.whiteColor,
                      ),
                      child: Column(
                        children: [
                          const SizedBox(height: 60),
                          Text(
                            user?.email ?? '',
                            style: TextStyle(
                                fontWeight: FontWeight.bold, fontSize: 20.sp),
                          ),
                          TextButton.icon(
                            onPressed: () {
                              Get.lazyPut(() => UpdateProfleController());
                              Get.to(() => UpdateProfileScreen());
                            },
                            label: const Text('تعديل'),
                            icon: const Icon(
                              Icons.edit,
                              size: 15,
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 16),
                            child: Column(
                              children: [
                                TextRichWidget(
                                  title: 'الاسم: ',
                                  titleValue: user?.name ?? '',
                                ),
                                TextRichWidget(
                                    title: 'الجوال: ',
                                    titleValue: user?.phoneNumber ?? ''),
                              ],
                            ),
                          ),
                          const Spacer(),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 16, vertical: 16),
                            child: ReactiveButton(
                              onPressed: () async {
                                // Show confirmation dialog before logout
                                final bool? shouldLogout = await DialogUtils.showLogoutConfirmationDialog();

                                // Only proceed if user confirmed
                                if (shouldLogout == true) {
                                  try {
                                    final reactiveButtonController =
                                        Get.find<ReactiveButtonController>();
                                    reactiveButtonController.isLoading.value = true;
                                    final LogoutAccountController
                                        logoutAccountController =
                                        Get.put(LogoutAccountController());
                                    await logoutAccountController.logoutUser();
                                    reactiveButtonController.isLoading.value =
                                        false;
                                  } catch (e) {
                                    // Fallback if ReactiveButtonController is not available
                                    final LogoutAccountController
                                        logoutAccountController =
                                        Get.put(LogoutAccountController());
                                    await logoutAccountController.logoutUser();
                                  }
                                }
                              },
                              color: AppColor.redColor,
                              wdth: double.infinity,
                              text: 'تسجيل الخروج',
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    top: -50, // Adjust to make avatar more visible
                    left: 0,
                    right: 0,
                    child: CircleAvatar(
                      radius: 50,
                      backgroundColor: AppColor.redColor,
                      child: user == null
                          ? const CircularProgressIndicator()
                          : CachedNetworkImage(
                              errorWidget: (context, url, error) {
                                return const Icon(
                                  Icons.error,
                                  color: Colors.white,
                                );
                              },
                              progressIndicatorBuilder:
                                  (context, url, progress) => const Center(
                                      child: CircularProgressIndicator()),
                              imageUrl: ApiList.baseUrl + user.img!,
                              imageBuilder: (context, imageProvider) =>
                                  Container(
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      image: DecorationImage(
                                        image: imageProvider,
                                        fit: BoxFit.contain,
                                      ),
                                    ),
                                  )),
                    ),
                  ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }
}
