import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/auth/controller/login_controller.dart';
import 'package:ma3ak/app/modules/auth/controller/register_controller.dart';
import 'package:ma3ak/app/modules/update_profile/controller/reactive_button_controller.dart';

/// Dependency injection bindings for authentication module
/// Registers all controllers needed for login, register, and auth functionality
class AuthBinding extends Bindings {
  @override
  void dependencies() {
    // Register ReactiveButtonController first to prevent dependency injection errors
    // This controller is used by AuthController and other components for loading states
    Get.lazyPut(() => ReactiveButtonController());
    
    // Register authentication controllers
    Get.lazyPut(() => AuthController());           // Main authentication logic
    Get.lazyPut(() => LoginController());          // Login form state management
    Get.lazyPut(() => RegisterController());       // Registration form state management
  }
}
