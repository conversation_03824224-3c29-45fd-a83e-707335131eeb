import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:ma3ak/app/modules/auth/controller/auth_controller.dart';
import 'package:ma3ak/app/modules/auth/controller/login_controller.dart';
import 'package:ma3ak/config/routes/app_routes.dart';
import 'package:ma3ak/core/utils/validation_utils.dart';
import 'package:ma3ak/core/widgets/loading_widgets.dart';
import 'package:ma3ak/app/modules/auth/widgets/text_form_field_auth.dart';

import 'package:ma3ak/config/theme/app_color.dart';
import 'package:ma3ak/utils/images_pic.dart';
import 'package:ma3ak/widgets/widgets/basic_app_bar.dart';

// User login screen with form validation and authentication
class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  // Builds the login screen with form and authentication logic
  @override
  Widget build(BuildContext context) {
    // Initialize controllers for login functionality
    final loginController = Get.put(LoginController());
    final authController = Get.put(AuthController());

    // Load saved credentials for auto-fill
    WidgetsBinding.instance.addPostFrameCallback((_) {
      authController.loadSavedCredentials();
    });

    var wdth = MediaQuery.of(context).size.width;
    var hgt = MediaQuery.of(context).size.height;

    return Scaffold(
        backgroundColor: AppColor.grayColor,
        appBar: const BasicAppbar(
          title: Text('تسجيل الدخول'),
        ),
        body: SingleChildScrollView(
            child: Center(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            // Main login form container
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              height: hgt * .75,
              decoration: BoxDecoration(
                color: AppColor.whiteColor,
                borderRadius: BorderRadius.circular(20),
              ),
              child: Form(
                key: loginController.formKey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Image.asset(
                      ImagesPic.logoM,
                      height: 100,
                      width: 100,
                    ),
                    const Text(
                      'أهلاً وسهلاً بك في معاك ',
                      // style: TextStyle(color: Colors.white),
                    ),
                    Column(
                      children: [
                        TextFormFieldAuth(
                          controller: authController.emailController,
                          labelText: 'البريد الإلكتروني',
                          prefixIcon: const Icon(Icons.person),
                          validator: ValidationUtils.validateEmail,
                        ),
                        const SizedBox(
                          height: 20,
                        ),
                        Obx(
                          () => TextFormFieldAuth(
                            controller: authController.passwordController,
                            labelText: 'كلمة المرور',
                            prefixIcon: const Icon(Icons.person),
                            obscureText:
                                !loginController.isPasswordVisible.value,
                            suffixIcon: IconButton(
                              icon: Icon(
                                loginController.isPasswordVisible.value
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                              ),
                              onPressed:
                                  loginController.togglePasswordVisibility,
                            ),
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'الرجاء إدخال كلمة المرور';
                              }
                              if (value.length < 6) {
                                return 'كلمة المرور يجب أن تكون 6 أحرف أو أكثر';
                              }
                              return null; // No errors
                            },
                          ),
                        ),
                      ],
                    ),
                    Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Obx(
                                () => Checkbox(
                                  value:
                                      loginController.isRememberMeChecked.value,
                                  onChanged: (value) {
                                    loginController.toggleRememberMe();
                                  },
                                ),
                              ),
                              const Text('تذكرني'),
                            ],
                          ),
                          TextButton(
                            onPressed: () {},
                            child: const Text('نسيت كلمة السر ؟'),
                          )
                        ]),
                    Obx(() {
                      return LoadingWidgets.loadingButton(
                        text: 'تسجيل',
                        width: wdth,
                        isLoading: loginController.isLoading.value,
                        onPressed: () async {
                          if (loginController.formKey.currentState!
                              .validate()) {
                            loginController.isLoading.value = true;
                            await authController.loginUser(
                              authController.emailController.text,
                              authController.passwordController.text,
                              rememberMe: loginController.isRememberMeChecked.value,
                            );
                            loginController.isLoading.value = false;
                          }
                        },
                      );
                    }),
                    Column(
                      children: [
                        TextButton(
                          onPressed: () {
                            Get.offNamed(Routes.register);
                          },
                          child: const Text(
                            'انشاء حساب جديد !',
                          ),
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        )));
  }
}
